# Performance Monitoring and Optimization

This document describes the comprehensive performance monitoring and optimization system implemented for the arbitrage bot to ensure <100ms latency for arbitrage detection.

## Overview

The performance monitoring system consists of three main components:

1. **Performance Monitor** - Real-time metrics collection and alerting
2. **Performance Optimizer** - Automatic optimization recommendations and tuning
3. **Performance Dashboard** - Real-time visualization and reporting

## Key Features

### 🎯 Latency Monitoring
- Real-time latency measurement with <100ms target
- Component-level execution time tracking
- P95 and average latency calculations
- Automatic alerts when thresholds are exceeded

### 📊 System Metrics
- CPU and memory usage monitoring
- Network I/O tracking
- Message throughput measurement
- Error rate calculation

### 🔧 Automatic Optimization
- Performance issue detection
- Optimization recommendations
- Automatic parameter tuning
- A/B testing for optimizations

### 🚨 Alerting System
- Configurable alert thresholds
- Multiple severity levels (warning, critical)
- Real-time notifications
- Performance degradation detection

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Performance     │    │ Performance     │    │ Performance     │
│ Monitor         │───▶│ Optimizer       │───▶│ Dashboard       │
│                 │    │                 │    │                 │
│ • Metrics       │    │ • Analysis      │    │ • Visualization │
│ • Latency       │    │ • Tuning        │    │ • Reporting     │
│ • Alerts        │    │ • Recommendations│    │ • Real-time UI  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Application Components                       │
│                                                                 │
│  WebSocket Manager  │  Arbitrage Engine  │  Risk Manager       │
│  Profit Calculator  │  Database Layer    │  Notification       │
└─────────────────────────────────────────────────────────────────┘
```

## Performance Requirements

### Latency Targets
- **Arbitrage Detection**: <100ms end-to-end
- **Profit Calculation**: <50ms average
- **Risk Assessment**: <25ms average
- **Data Normalization**: <10ms average

### Throughput Targets
- **Message Processing**: >100 messages/second
- **WebSocket Data**: >50 updates/second per exchange
- **Database Operations**: <10ms per query

### Resource Limits
- **CPU Usage**: <80% sustained
- **Memory Usage**: <1GB total
- **Network Latency**: <50ms to exchanges

## Usage

### Starting Performance Monitoring

```python
from src.monitoring import performance_monitor, performance_optimizer, performance_dashboard

# Start monitoring
await performance_monitor.start_monitoring(interval_seconds=1.0)
await performance_optimizer.start_optimization(interval_minutes=30)
await performance_dashboard.start_dashboard(update_interval=5.0)
```

### Measuring Component Latency

```python
# Using context manager
async with performance_monitor.measure_latency("component_name"):
    # Your code here
    result = await some_operation()

# Manual recording
start_time = time.perf_counter()
result = await some_operation()
end_time = time.perf_counter()
latency_ms = (end_time - start_time) * 1000
performance_monitor.record_latency(latency_ms)
```

### Adding Alert Callbacks

```python
async def alert_handler(alert_type: str, alert_data: dict):
    print(f"Alert: {alert_type} - {alert_data['message']}")

performance_monitor.add_alert_callback(alert_handler)
```

### Getting Performance Data

```python
# Current metrics
current_metrics = performance_monitor.get_current_metrics()

# Historical data
history = performance_monitor.get_metrics_history(minutes=60)

# Component metrics
components = performance_monitor.get_component_metrics()

# Dashboard data
dashboard_data = performance_dashboard.get_dashboard_data()
```

## Command Line Tools

### Performance Benchmark

Run comprehensive performance benchmarks:

```bash
python scripts/performance_benchmark.py
```

This will test:
- Profit calculation latency
- Risk assessment speed
- Data normalization performance
- End-to-end arbitrage detection
- System throughput

### Real-time Monitor

Start real-time performance monitoring:

```bash
python scripts/performance_monitor_cli.py
```

Features:
- Live performance dashboard
- Real-time latency monitoring
- Component status tracking
- Alert notifications

## Configuration

### Performance Monitor Settings

```python
# In src/monitoring/performance_monitor.py
monitor = PerformanceMonitor(
    alert_threshold_ms=100.0,  # Latency alert threshold
)

await monitor.start_monitoring(
    interval_seconds=1.0  # Metrics collection interval
)
```

### Optimizer Settings

```python
# In src/monitoring/optimizer.py
optimizer = PerformanceOptimizer(
    optimization_level=OptimizationLevel.MODERATE
)

await optimizer.start_optimization(
    interval_minutes=30  # Optimization cycle interval
)
```

### Dashboard Settings

```python
# In src/monitoring/dashboard.py
dashboard = PerformanceDashboard()

await dashboard.start_dashboard(
    update_interval=5.0  # Dashboard update interval
)
```

## Optimization Strategies

### Automatic Optimizations

The system automatically applies optimizations based on detected issues:

1. **High Latency**
   - Reduce WebSocket ping intervals
   - Increase message batch sizes
   - Optimize processing pipelines

2. **High CPU Usage**
   - Increase thread pool sizes
   - Optimize algorithms
   - Add caching layers

3. **High Memory Usage**
   - Implement memory-efficient caching
   - Add data cleanup routines
   - Optimize data structures

4. **High Error Rates**
   - Improve error handling
   - Add circuit breakers
   - Increase retry mechanisms

### Manual Tuning

Key parameters that can be manually tuned:

```python
# WebSocket settings
websocket_ping_interval: 10-60 seconds
websocket_reconnect_delay: 0.5-5.0 seconds

# Processing settings
message_batch_size: 1-100 messages
processing_thread_pool_size: 1-16 threads

# Caching settings
profit_calculation_cache_size: 100-10000 entries
risk_assessment_timeout: 1-10 seconds
```

## Monitoring Metrics

### Core Metrics

- **Latency**: End-to-end processing time
- **Throughput**: Messages processed per second
- **Error Rate**: Percentage of failed operations
- **CPU Usage**: Processor utilization
- **Memory Usage**: RAM consumption
- **Network I/O**: Bytes sent/received

### Component Metrics

- **Average Execution Time**: Mean processing time
- **P95 Execution Time**: 95th percentile latency
- **Success Rate**: Percentage of successful operations
- **Throughput**: Operations per second
- **Last Execution**: Timestamp of last operation

### System Health

- **Overall Status**: healthy/warning/critical
- **Active Connections**: Number of WebSocket connections
- **Data Freshness**: Age of latest data
- **Circuit Breaker Status**: Open/closed state

## Troubleshooting

### High Latency Issues

1. Check component execution times
2. Verify network connectivity
3. Review database query performance
4. Check for resource contention

### High CPU Usage

1. Review algorithm efficiency
2. Check for infinite loops
3. Optimize data processing
4. Increase thread pool sizes

### Memory Leaks

1. Monitor memory growth over time
2. Check for unclosed resources
3. Review caching strategies
4. Implement garbage collection

### Connection Issues

1. Verify WebSocket connectivity
2. Check network stability
3. Review reconnection logic
4. Monitor circuit breaker status

## Best Practices

### Performance Optimization

1. **Measure First**: Always measure before optimizing
2. **Profile Regularly**: Use continuous profiling
3. **Optimize Bottlenecks**: Focus on slowest components
4. **Test Changes**: Validate optimizations with benchmarks

### Monitoring Setup

1. **Set Appropriate Thresholds**: Avoid alert fatigue
2. **Monitor Key Metrics**: Focus on business-critical metrics
3. **Use Dashboards**: Visualize trends and patterns
4. **Automate Responses**: Implement automatic remediation

### Resource Management

1. **Limit Resource Usage**: Set appropriate limits
2. **Monitor Growth**: Track resource usage trends
3. **Plan Capacity**: Anticipate scaling needs
4. **Optimize Regularly**: Continuous improvement

## Integration

The performance monitoring system is integrated into all major components:

- **WebSocket Manager**: Latency and throughput monitoring
- **Arbitrage Engine**: End-to-end detection timing
- **Profit Calculator**: Calculation performance tracking
- **Risk Manager**: Assessment speed monitoring
- **Database Layer**: Query performance measurement

This ensures comprehensive coverage and accurate performance measurement across the entire system.
