"""
MEXC WebSocket implementation for real-time ticker data.
"""
import json
from typing import Dict, Any, List
from datetime import datetime

from src.websocket.base import BaseWebSocketManager, WebSocketConfig
from src.websocket.data_normalizer import <PERSON>Normalizer, NormalizedTickerData
from src.utils.logger import logger


class MexcWebSocketManager(BaseWebSocketManager):
    """MEXC WebSocket manager for ticker data."""
    
    def __init__(self, config: WebSocketConfig):
        super().__init__("mexc", config)
        self.subscribed_symbols: List[str] = []
        self.last_ping_id = 0
    
    async def _on_connected(self):
        """Called when WebSocket connection is established."""
        logger.info(f"{self.exchange_name}: WebSocket connected, ready for subscriptions")
    
    async def _on_message(self, data: Dict[str, Any]):
        """Handle incoming WebSocket message."""
        try:
            # MEXC sends different message types
            if "stream" in data and "data" in data:
                # Ticker data stream
                await self._handle_ticker_stream(data)
            
            elif "id" in data and "result" in data:
                # Response to subscription/ping
                await self._handle_response(data)
            
            elif "error" in data:
                logger.error(f"{self.exchange_name}: WebSocket error: {data['error']}")
            
            else:
                logger.debug(f"{self.exchange_name}: Unhandled message type: {data}")
            
        except Exception as e:
            logger.error(f"{self.exchange_name}: Error processing message: {e}")
    
    async def _send_ping(self):
        """Send ping message to maintain connection."""
        self.last_ping_id += 1
        ping_message = {
            "id": self.last_ping_id,
            "method": "PING"
        }
        await self.send_message(ping_message)
    
    def _is_pong_message(self, data: Dict[str, Any]) -> bool:
        """Check if message is a pong response."""
        return (data.get("result") == "pong" or 
                data.get("method") == "PONG")
    
    def _get_message_type(self, data: Dict[str, Any]) -> str:
        """Extract message type from received data."""
        if "stream" in data:
            return "ticker"
        elif "result" in data:
            return "response"
        elif "error" in data:
            return "error"
        return "unknown"
    
    async def subscribe_ticker(self, symbol: str) -> bool:
        """Subscribe to ticker updates for a symbol."""
        try:
            # Convert symbol format (e.g., "ADA/USDT" -> "ADAUSDT")
            mexc_symbol = symbol.replace("/", "").upper()
            
            subscription_message = {
                "id": self.last_ping_id + 1,
                "method": "SUBSCRIPTION",
                "params": [f"{mexc_symbol}@ticker"]
            }
            self.last_ping_id += 1
            
            success = await self.send_message(subscription_message)
            if success:
                self.subscribed_symbols.append(symbol)
                logger.info(f"{self.exchange_name}: Subscribed to ticker for {symbol}")
            
            return success
            
        except Exception as e:
            logger.error(f"{self.exchange_name}: Failed to subscribe to {symbol}: {e}")
            return False
    
    async def unsubscribe_ticker(self, symbol: str) -> bool:
        """Unsubscribe from ticker updates for a symbol."""
        try:
            mexc_symbol = symbol.replace("/", "").upper()
            
            unsubscription_message = {
                "id": self.last_ping_id + 1,
                "method": "UNSUBSCRIPTION",
                "params": [f"{mexc_symbol}@ticker"]
            }
            self.last_ping_id += 1
            
            success = await self.send_message(unsubscription_message)
            if success and symbol in self.subscribed_symbols:
                self.subscribed_symbols.remove(symbol)
                logger.info(f"{self.exchange_name}: Unsubscribed from ticker for {symbol}")
            
            return success
            
        except Exception as e:
            logger.error(f"{self.exchange_name}: Failed to unsubscribe from {symbol}: {e}")
            return False
    
    async def _handle_ticker_stream(self, data: Dict[str, Any]):
        """Handle ticker stream message."""
        try:
            stream = data.get("stream", "")
            if "@ticker" in stream:
                # Extract symbol from stream name
                mexc_symbol = stream.split("@")[0].upper()
                
                # Convert back to standard format
                # This is a simplified conversion - might need more sophisticated logic
                if mexc_symbol.endswith("USDT"):
                    base = mexc_symbol[:-4]
                    symbol = f"{base}/USDT"
                else:
                    # Handle other quote currencies if needed
                    symbol = mexc_symbol  # Fallback
                
                # Normalize the data
                normalized_data = DataNormalizer.normalize_mexc_ticker(data, symbol)
                
                if normalized_data and DataNormalizer.validate_ticker_data(normalized_data):
                    # Store in database
                    price_data = DataNormalizer.to_price_data(normalized_data)
                    await self._store_price_data(price_data)
                    
                    # Notify handlers
                    await self._notify_data_handlers(normalized_data)
                    
                    logger.debug(f"{self.exchange_name}: Processed ticker for {symbol}")
                else:
                    logger.warning(f"{self.exchange_name}: Invalid ticker data for {symbol}")
        
        except Exception as e:
            logger.error(f"{self.exchange_name}: Error handling ticker stream: {e}")
    
    async def _handle_response(self, data: Dict[str, Any]):
        """Handle response to subscription or ping."""
        try:
            result = data.get("result")
            request_id = data.get("id")
            
            if result == "pong":
                logger.debug(f"{self.exchange_name}: Received pong response")
            elif result is not None:
                logger.debug(f"{self.exchange_name}: Response (ID: {request_id}): {result}")
            
        except Exception as e:
            logger.error(f"{self.exchange_name}: Error handling response: {e}")
    
    async def _store_price_data(self, price_data):
        """Store price data in database."""
        try:
            from src.database.models import db_manager
            await db_manager.insert_price(price_data)
        except Exception as e:
            logger.error(f"{self.exchange_name}: Failed to store price data: {e}")
    
    async def _notify_data_handlers(self, normalized_data: NormalizedTickerData):
        """Notify registered data handlers."""
        # Call the data handler if set by the manager
        if hasattr(self, '_data_handler') and self._data_handler:
            try:
                await self._data_handler(normalized_data)
            except Exception as e:
                logger.error(f"{self.exchange_name}: Error in data handler: {e}")

    def set_data_handler(self, handler):
        """Set the data handler callback."""
        self._data_handler = handler


def create_mexc_websocket(exchange_config) -> MexcWebSocketManager:
    """Create MEXC WebSocket manager with configuration."""
    # MEXC WebSocket URL
    ws_url = "wss://wbs.mexc.com/ws"
    
    config = WebSocketConfig(
        url=ws_url,
        ping_interval=30,
        ping_timeout=10,
        max_reconnect_attempts=10,
        initial_reconnect_delay=1.0,
        max_reconnect_delay=60.0,
        reconnect_backoff_factor=2.0,
        connection_timeout=exchange_config.timeout,
        message_timeout=5
    )
    
    return MexcWebSocketManager(config)
