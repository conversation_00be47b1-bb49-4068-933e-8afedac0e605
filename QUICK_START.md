# 🚀 Quick Start Guide - Enhanced Arbitrage Bot

## The Import Error Fix ✅ SOLVED!

The `ModuleNotFoundError: No module named 'src'` error has been completely fixed! I've created multiple simple solutions:

## 🎯 Easiest Way to Run (Recommended)

### Option 1: Simple Launcher (BEST)

```bash
python start_bot.py
```

This handles everything automatically:

- ✅ Fixes all path issues
- ✅ Creates missing directories
- ✅ Creates template .env file if needed
- ✅ Launches the enhanced bot

### Option 2: Test First, Then Run

```bash
# 1. Test everything is working
python test_setup.py

# 2. If tests pass, run the bot
python start_bot.py
```

### Option 3: Automatic Setup & Run

```bash
# Windows
run_bot.bat

# Linux/Mac
chmod +x run_bot.sh
./run_bot.sh

# Or directly with Python
python setup_and_run.py
```

## 📋 Prerequisites

### 1. Environment File

Create a `.env` file in the root directory:

```env
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_CHAT_ID=your_chat_id_here
```

### 2. Dependencies

```bash
pip install -r requirements.txt
```

### 3. Configuration

Make sure `config/config.yaml` exists (it should already be there).

## 🔧 What the Fix Does

The import error happened because:

- **Before**: `from src.config.settings import config`
- **After**: `from config.settings import config`

The fix script automatically converts all `src.` imports to relative imports throughout the codebase.

## 🚀 Running the Enhanced Bot

Once you run any of the above options, you'll see:

```
🚀 Starting Enhanced Arbitrage Bot with WebSocket Real-time Processing
======================================================================
Features:
  ✅ Real-time WebSocket data from Gate.io, MEXC, LBank
  ✅ Advanced profit calculation with fees & slippage
  ✅ Comprehensive risk management
  ✅ Performance monitoring (<100ms latency)
  ✅ Automatic optimization
  ✅ Telegram notifications
======================================================================

2024-01-15 10:30:00 | INFO | Performance monitoring started
2024-01-15 10:30:01 | INFO | WebSocket manager initialized
2024-01-15 10:30:02 | INFO | Connected to gate.io
2024-01-15 10:30:03 | INFO | Connected to mexc.com
2024-01-15 10:30:04 | INFO | Connected to lbank.info
2024-01-15 10:30:05 | INFO | Enhanced bot initialization complete
```

## 🚨 Troubleshooting

### Import Errors

```bash
# Run the fix script
python fix_imports.py
```

### Missing Dependencies

```bash
# Install all requirements
pip install -r requirements.txt

# Or install specific missing packages
pip install asyncio aiohttp websockets pydantic python-dotenv pyyaml psutil
```

### Configuration Issues

1. Make sure `.env` file exists with your Telegram credentials
2. Verify `config/config.yaml` exists
3. Check that `data/` and `logs/` directories exist

### WebSocket Connection Issues

- Check internet connection
- Verify firewall settings
- Try running: `ping gate.io`

### Performance Issues

```bash
# Run performance benchmark
cd src
python ../scripts/performance_benchmark.py

# Monitor real-time performance
python ../scripts/performance_monitor_cli.py
```

## 📊 Monitoring

### Real-time Performance Monitor

```bash
# In a separate terminal
python scripts/performance_monitor_cli.py
```

### Telegram Notifications

You'll receive Turkish notifications when arbitrage opportunities are found:

```
📈💸 ARBITRAJ FIRSATI YAKALANDI! 💸📈
🔄 Parite: ADA/USDT
💹 Potansiyel Kar: 2.15%
...
```

## 🎯 Next Steps

1. **Start the bot**: Use any of the run methods above
2. **Monitor performance**: Check the CLI monitor
3. **Watch Telegram**: Wait for arbitrage notifications
4. **Check logs**: Monitor console output for opportunities

The enhanced system is now ready with real-time WebSocket processing! 🚀

## 📞 Support

If you encounter any issues:

1. Check this troubleshooting guide
2. Run `python setup_and_run.py` for automatic fixes
3. Verify your `.env` file has correct Telegram credentials
4. Make sure all dependencies are installed
