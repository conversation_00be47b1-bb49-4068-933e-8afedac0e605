"""
Telegram bot for sending arbitrage notifications.
"""
import asyncio
from datetime import datetime
from typing import Optional, Dict, List
import telegram
from telegram.ext import Application
from telegram.error import TelegramError
import html

from src.config.settings import config
from src.database.models import ArbitrageSignal, db_manager
from src.utils.logger import logger


class TelegramNotifier:
    """Telegram bot for sending arbitrage notifications."""
    
    def __init__(self):
        self.bot = None
        self.chat_id = None
        self.is_configured = False
        self.last_error = None
        
    async def initialize(self):
        """Initialize Telegram bot."""
        try:
            if not config.is_telegram_configured():
                logger.warning("Telegram not configured - notifications disabled")
                return

            # Initialize bot
            token = config.settings.telegram_bot_token
            self.chat_id = config.settings.telegram_chat_id

            # Create custom request with SSL verification disabled and timeouts
            custom_request = telegram.request.HTTPXRequest(
                read_timeout=30,
                write_timeout=30,
                httpx_kwargs={'verify': False}
            )

            # Create application with custom request
            application = (Application.builder()
                          .token(token)
                          .request(custom_request)
                          .build())

            self.bot = application.bot

            # Test connection
            await self._test_connection()

            self.is_configured = True
            logger.info("Telegram bot initialized successfully")

        except Exception as e:
            self.last_error = str(e)
            logger.error(f"Failed to initialize Telegram bot: {e}")
            self.is_configured = False
    
    async def _test_connection(self):
        """Test Telegram bot connection."""
        try:
            bot_info = await self.bot.get_me()
            logger.info(f"Connected to Telegram bot: @{bot_info.username}")
            
            # Send test message to verify chat access
            test_message = "🤖 Arbitrage bot connected successfully!"
            await self.bot.send_message(
                chat_id=self.chat_id,
                text=test_message,
                parse_mode='HTML'
            )
            
        except TelegramError as e:
            logger.error(f"Telegram connection test failed: {e}")
            raise
    
    async def send_arbitrage_signal(self, signal: ArbitrageSignal) -> bool:
        """Send arbitrage signal to Telegram."""
        if not self.is_configured:
            logger.warning("Telegram not configured - cannot send signal")
            return False
        
        try:
            message = self._format_arbitrage_message(signal)
            
            await self.bot.send_message(
                chat_id=self.chat_id,
                text=message,
                parse_mode=config.telegram.parse_mode,
                disable_web_page_preview=config.telegram.disable_web_page_preview
            )
            
            # Update signal status in database
            if signal.id:
                await db_manager.update_signal_telegram_status(signal.id, True)
            
            logger.info(f"Sent Telegram signal for {signal.symbol}: {signal.profit_percent}%")
            return True
            
        except TelegramError as e:
            self.last_error = str(e)
            logger.error(f"Failed to send Telegram message: {e}")
            return False
        except Exception as e:
            self.last_error = str(e)
            logger.error(f"Unexpected error sending Telegram message: {e}")
            return False
    
    def _format_arbitrage_message(self, signal: ArbitrageSignal) -> str:
        """Format arbitrage signal for Telegram message."""
        template = config.telegram.message_template

        # Prepare data for template with enhanced fields
        data = {
            'symbol': html.escape(signal.symbol),
            'profit_percent': f"{signal.profit_percent:.2f}",
            'action': signal.action,
            'buy_exchange': html.escape(signal.buy_exchange.lower()),
            'sell_exchange': html.escape(signal.sell_exchange.lower()),
            'buy_price': f"{signal.buy_price}",
            'sell_price': f"{signal.sell_price}",
            'price_diff': f"{signal.profit_amount:.6f}",
            'timestamp': signal.timestamp.strftime("%H:%M:%S UTC"),
            # Enhanced template fields
            'buy_current_profit': f"{signal.buy_current_profit:.2f}",
            'sell_current_profit': f"{signal.sell_current_profit:.2f}",
            'buy_deviation': f"{signal.buy_deviation:+.2f}",
            'sell_deviation': f"{signal.sell_deviation:+.2f}",
            'sell_limit': f"{signal.sell_limit:.0f}",
            'period': signal.period,
            'exchange_rate': f"{signal.exchange_rate:.2f}",
            'commission_rate': f"{signal.commission_rate:.2f}"
        }

        try:
            return template.format(**data)
        except KeyError as e:
            logger.error(f"Template formatting error: {e}")
            # Fallback to simple message
            return self._create_fallback_message(signal)
    
    def _create_fallback_message(self, signal: ArbitrageSignal) -> str:
        """Create fallback message if template fails."""
        return f"""📗|{html.escape(signal.buy_exchange.lower())}| - UZUN
Mevcut: %{signal.buy_current_profit:.2f}
Sapma: %{signal.buy_deviation:+.2f}
🕐Dönem: {signal.period}

📕|{html.escape(signal.sell_exchange.lower())}| - KISA
Mevcut: %{signal.sell_current_profit:.2f}
Sapma: %{signal.sell_deviation:+.2f}
⛔️Limit: {signal.sell_limit:.0f}$
🕐Dönem: {signal.period}

💰Kur farkı:
Mevcut: %{signal.profit_percent:.2f}
Kur oranı: %{signal.exchange_rate:.2f} ({signal.buy_price:.0f} | {signal.sell_price:.0f})
(Komisyon: %{signal.commission_rate:.2f})

📈Grafik"""
    
    async def send_batch_signals(self, signals: List[ArbitrageSignal]) -> Dict[str, int]:
        """Send multiple arbitrage signals."""
        results = {'sent': 0, 'failed': 0}
        
        if not self.is_configured:
            logger.warning("Telegram not configured - cannot send batch signals")
            return results
        
        for signal in signals:
            success = await self.send_arbitrage_signal(signal)
            if success:
                results['sent'] += 1
            else:
                results['failed'] += 1
            
            # Small delay to avoid rate limiting
            await asyncio.sleep(0.5)
        
        logger.info(f"Batch signal results: {results['sent']} sent, {results['failed']} failed")
        return results
    
    async def send_status_update(self, message: str) -> bool:
        """Send status update message."""
        if not self.is_configured:
            return False
        
        try:
            formatted_message = f"ℹ️ <b>Bot Status</b>\n\n{html.escape(message)}"
            
            await self.bot.send_message(
                chat_id=self.chat_id,
                text=formatted_message,
                parse_mode='HTML'
            )
            
            return True
            
        except TelegramError as e:
            logger.error(f"Failed to send status update: {e}")
            return False
    
    async def send_error_notification(self, error_message: str) -> bool:
        """Send error notification."""
        if not self.is_configured:
            return False
        
        try:
            formatted_message = f"❌ <b>Bot Error</b>\n\n{html.escape(error_message)}"
            
            await self.bot.send_message(
                chat_id=self.chat_id,
                text=formatted_message,
                parse_mode='HTML'
            )
            
            return True
            
        except TelegramError as e:
            logger.error(f"Failed to send error notification: {e}")
            return False
    
    async def send_daily_summary(self, summary_data: Dict) -> bool:
        """Send daily summary report."""
        if not self.is_configured:
            return False
        
        try:
            message = self._format_daily_summary(summary_data)
            
            await self.bot.send_message(
                chat_id=self.chat_id,
                text=message,
                parse_mode='HTML'
            )
            
            logger.info("Sent daily summary to Telegram")
            return True
            
        except TelegramError as e:
            logger.error(f"Failed to send daily summary: {e}")
            return False
    
    def _format_daily_summary(self, data: Dict) -> str:
        """Format daily summary message."""
        message = "📊 <b>Daily Arbitrage Summary</b>\n\n"
        
        # Basic stats
        message += f"🔢 <b>Total Signals:</b> {data.get('total_signals', 0)}\n"
        message += f"📈 <b>Average Profit:</b> {data.get('average_profit', 0)}%\n"
        
        # Best opportunity
        best = data.get('best_opportunity')
        if best:
            message += f"🏆 <b>Best Opportunity:</b> {best['symbol']} ({best['profit_percent']}%)\n"
            message += f"   {best['buy_exchange']} → {best['sell_exchange']}\n"
        
        message += "\n"
        
        # Signals by symbol
        symbols = data.get('signals_by_symbol', {})
        if symbols:
            message += "<b>📊 By Symbol:</b>\n"
            for symbol, stats in symbols.items():
                message += f"• {symbol}: {stats['count']} signals ({stats['average_profit']}% avg)\n"
        
        message += "\n"
        
        # Exchange pairs
        pairs = data.get('exchange_pairs', {})
        if pairs:
            message += "<b>🔄 Top Exchange Pairs:</b>\n"
            sorted_pairs = sorted(pairs.items(), key=lambda x: x[1]['count'], reverse=True)[:3]
            for pair, stats in sorted_pairs:
                message += f"• {pair}: {stats['count']} signals ({stats['average_profit']}% avg)\n"
        
        message += f"\n⏰ <b>Report Time:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}"
        
        return message
    
    async def send_market_overview(self, overview: Dict) -> bool:
        """Send market overview."""
        if not self.is_configured:
            return False
        
        try:
            message = self._format_market_overview(overview)
            
            await self.bot.send_message(
                chat_id=self.chat_id,
                text=message,
                parse_mode='HTML'
            )
            
            return True
            
        except TelegramError as e:
            logger.error(f"Failed to send market overview: {e}")
            return False
    
    def _format_market_overview(self, overview: Dict) -> str:
        """Format market overview message."""
        message = "📈 <b>Market Overview</b>\n\n"
        
        # Connected exchanges
        exchanges = overview.get('exchanges', [])
        message += f"🔗 <b>Connected Exchanges:</b> {', '.join(exchanges)}\n\n"
        
        # Symbol data
        symbols = overview.get('symbols', {})
        for symbol, data in symbols.items():
            message += f"<b>{symbol}</b>\n"
            
            # Prices by exchange
            prices = data.get('prices', {})
            for exchange, price in prices.items():
                message += f"  {exchange}: ${price:.6f}\n"
            
            # Spread info
            spread = data.get('spread_percent', 0)
            message += f"  📊 Spread: {spread}%\n\n"
        
        message += f"⏰ <b>Updated:</b> {datetime.now().strftime('%H:%M:%S UTC')}"
        
        return message
    
    def get_status(self) -> Dict:
        """Get Telegram bot status."""
        return {
            'configured': self.is_configured,
            'last_error': self.last_error,
            'chat_id': self.chat_id,
            'bot_username': getattr(self.bot, 'username', None) if self.bot else None
        }


# Global Telegram notifier instance
telegram_notifier = TelegramNotifier()
