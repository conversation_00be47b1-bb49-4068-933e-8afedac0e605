"""
WebSocket manager for coordinating multiple exchange connections.
"""
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Set
from collections import defaultdict

from src.config.settings import config
from src.websocket.base import BaseWebSocketManager, ConnectionState
from src.websocket.data_normalizer import NormalizedTickerData, DataNormalizer
from src.database.models import db_manager
from src.utils.logger import logger


class WebSocketManager:
    """Manages WebSocket connections for multiple exchanges."""
    
    def __init__(self):
        self.connections: Dict[str, BaseWebSocketManager] = {}
        self.subscribed_symbols: Set[str] = set()
        self.data_handlers: List[Callable[[NormalizedTickerData], None]] = []
        self.error_handlers: List[Callable[[str, Exception], None]] = []
        
        # Data caching and statistics
        self.latest_data: Dict[str, Dict[str, NormalizedTickerData]] = defaultdict(dict)
        self.data_timestamps: Dict[str, Dict[str, datetime]] = defaultdict(dict)
        self.message_counts: Dict[str, int] = defaultdict(int)
        self.error_counts: Dict[str, int] = defaultdict(int)
        
        # Performance monitoring
        self.start_time = datetime.now()
        self.total_messages = 0
        self.last_cleanup_time = datetime.now()
        
        self.is_running = False
        self.cleanup_task: Optional[asyncio.Task] = None
    
    def add_connection(self, exchange_name: str, connection: BaseWebSocketManager):
        """Add a WebSocket connection for an exchange."""
        self.connections[exchange_name] = connection
        
        # Set up event handlers
        connection.add_message_handler("ticker", self._handle_ticker_message)
        connection.add_error_handler(lambda error: self._handle_error(exchange_name, error))
        connection.add_connection_handler(lambda: self._handle_connection(exchange_name))
        connection.add_disconnection_handler(lambda: self._handle_disconnection(exchange_name))
        
        logger.info(f"Added WebSocket connection for {exchange_name}")
    
    def add_data_handler(self, handler: Callable[[NormalizedTickerData], None]):
        """Add a handler for normalized ticker data."""
        self.data_handlers.append(handler)
    
    def add_error_handler(self, handler: Callable[[str, Exception], None]):
        """Add an error handler."""
        self.error_handlers.append(handler)
    
    async def start(self) -> bool:
        """Start all WebSocket connections."""
        if self.is_running:
            logger.warning("WebSocket manager already running")
            return True
        
        if not self.connections:
            logger.error("No WebSocket connections configured")
            return False
        
        self.is_running = True
        self.start_time = datetime.now()
        
        # Start all connections
        start_tasks = []
        for exchange_name, connection in self.connections.items():
            task = asyncio.create_task(self._start_connection(exchange_name, connection))
            start_tasks.append(task)
        
        # Wait for all connections to start
        results = await asyncio.gather(*start_tasks, return_exceptions=True)
        
        # Check results
        successful_connections = 0
        for i, result in enumerate(results):
            exchange_name = list(self.connections.keys())[i]
            if isinstance(result, Exception):
                logger.error(f"Failed to start {exchange_name}: {result}")
                self.error_counts[exchange_name] += 1
            elif result:
                successful_connections += 1
                logger.info(f"Successfully started {exchange_name}")
        
        if successful_connections == 0:
            logger.error("No WebSocket connections could be started")
            self.is_running = False
            return False
        
        # Start cleanup task
        self.cleanup_task = asyncio.create_task(self._cleanup_loop())
        
        logger.info(f"WebSocket manager started with {successful_connections}/{len(self.connections)} connections")
        return True
    
    async def stop(self):
        """Stop all WebSocket connections."""
        if not self.is_running:
            return
        
        self.is_running = False
        
        # Cancel cleanup task
        if self.cleanup_task and not self.cleanup_task.done():
            self.cleanup_task.cancel()
            try:
                await self.cleanup_task
            except asyncio.CancelledError:
                pass
        
        # Stop all connections
        stop_tasks = []
        for connection in self.connections.values():
            task = asyncio.create_task(connection.stop())
            stop_tasks.append(task)
        
        await asyncio.gather(*stop_tasks, return_exceptions=True)
        
        logger.info("WebSocket manager stopped")
    
    async def subscribe_to_symbol(self, symbol: str) -> bool:
        """Subscribe to ticker updates for a symbol on all exchanges."""
        if symbol in self.subscribed_symbols:
            logger.debug(f"Already subscribed to {symbol}")
            return True
        
        success_count = 0
        for exchange_name, connection in self.connections.items():
            if connection.state == ConnectionState.CONNECTED:
                try:
                    await self._subscribe_exchange_symbol(connection, exchange_name, symbol)
                    success_count += 1
                except Exception as e:
                    logger.error(f"Failed to subscribe {exchange_name} to {symbol}: {e}")
                    self.error_counts[exchange_name] += 1
        
        if success_count > 0:
            self.subscribed_symbols.add(symbol)
            logger.info(f"Subscribed to {symbol} on {success_count} exchanges")
            return True
        
        logger.error(f"Failed to subscribe to {symbol} on any exchange")
        return False
    
    async def unsubscribe_from_symbol(self, symbol: str) -> bool:
        """Unsubscribe from ticker updates for a symbol on all exchanges."""
        if symbol not in self.subscribed_symbols:
            logger.debug(f"Not subscribed to {symbol}")
            return True
        
        success_count = 0
        for exchange_name, connection in self.connections.items():
            if connection.state == ConnectionState.CONNECTED:
                try:
                    await self._unsubscribe_exchange_symbol(connection, exchange_name, symbol)
                    success_count += 1
                except Exception as e:
                    logger.error(f"Failed to unsubscribe {exchange_name} from {symbol}: {e}")
        
        self.subscribed_symbols.discard(symbol)
        
        # Clean up cached data
        for exchange_data in self.latest_data.values():
            exchange_data.pop(symbol, None)
        for exchange_timestamps in self.data_timestamps.values():
            exchange_timestamps.pop(symbol, None)
        
        logger.info(f"Unsubscribed from {symbol}")
        return True
    
    def get_latest_data(self, symbol: str) -> Dict[str, NormalizedTickerData]:
        """Get latest ticker data for a symbol from all exchanges."""
        result = {}
        for exchange_name, exchange_data in self.latest_data.items():
            if symbol in exchange_data:
                # Check if data is recent (within last 60 seconds)
                timestamp = self.data_timestamps[exchange_name].get(symbol)
                if timestamp and (datetime.now() - timestamp).total_seconds() < 60:
                    result[exchange_name] = exchange_data[symbol]
        
        return result
    
    def get_connected_exchanges(self) -> List[str]:
        """Get list of currently connected exchanges."""
        return [
            name for name, connection in self.connections.items()
            if connection.state == ConnectionState.CONNECTED
        ]
    
    def get_statistics(self) -> Dict[str, any]:
        """Get WebSocket manager statistics."""
        uptime = (datetime.now() - self.start_time).total_seconds()
        
        connection_stats = {}
        for exchange_name, connection in self.connections.items():
            connection_stats[exchange_name] = connection.get_stats()
        
        return {
            "uptime_seconds": uptime,
            "total_messages": self.total_messages,
            "subscribed_symbols": len(self.subscribed_symbols),
            "connected_exchanges": len(self.get_connected_exchanges()),
            "total_exchanges": len(self.connections),
            "message_counts": dict(self.message_counts),
            "error_counts": dict(self.error_counts),
            "connections": connection_stats,
            "cached_symbols": {
                exchange: list(data.keys()) 
                for exchange, data in self.latest_data.items()
            }
        }
    
    async def _start_connection(self, exchange_name: str, connection: BaseWebSocketManager) -> bool:
        """Start a single WebSocket connection."""
        try:
            success = await connection.start()
            if success:
                # Subscribe to existing symbols
                for symbol in self.subscribed_symbols:
                    try:
                        await self._subscribe_exchange_symbol(connection, exchange_name, symbol)
                    except Exception as e:
                        logger.error(f"Failed to subscribe {exchange_name} to {symbol}: {e}")
            return success
        except Exception as e:
            logger.error(f"Error starting {exchange_name}: {e}")
            return False
    
    async def _handle_ticker_message(self, data: Dict[str, any]):
        """Handle ticker message from any exchange."""
        # This will be implemented by exchange-specific connections
        pass
    
    async def _handle_connection(self, exchange_name: str):
        """Handle connection event."""
        logger.info(f"WebSocket connected: {exchange_name}")
        
        # Re-subscribe to symbols
        for symbol in self.subscribed_symbols:
            try:
                connection = self.connections[exchange_name]
                await self._subscribe_exchange_symbol(connection, exchange_name, symbol)
            except Exception as e:
                logger.error(f"Failed to re-subscribe {exchange_name} to {symbol}: {e}")
    
    async def _handle_disconnection(self, exchange_name: str):
        """Handle disconnection event."""
        logger.warning(f"WebSocket disconnected: {exchange_name}")
        
        # Clear cached data for this exchange
        self.latest_data[exchange_name].clear()
        self.data_timestamps[exchange_name].clear()
    
    async def _handle_error(self, exchange_name: str, error: Exception):
        """Handle error from exchange connection."""
        self.error_counts[exchange_name] += 1
        logger.error(f"WebSocket error from {exchange_name}: {error}")
        
        # Notify error handlers
        for handler in self.error_handlers:
            try:
                await handler(exchange_name, error)
            except Exception as e:
                logger.error(f"Error handler failed: {e}")
    
    async def _cleanup_loop(self):
        """Periodic cleanup of old data."""
        while self.is_running:
            try:
                await asyncio.sleep(60)  # Run every minute
                await self._cleanup_old_data()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Cleanup loop error: {e}")
    
    async def _cleanup_old_data(self):
        """Clean up old cached data."""
        current_time = datetime.now()
        cutoff_time = current_time - timedelta(minutes=5)
        
        cleaned_count = 0
        for exchange_name in list(self.data_timestamps.keys()):
            exchange_timestamps = self.data_timestamps[exchange_name]
            symbols_to_remove = []
            
            for symbol, timestamp in exchange_timestamps.items():
                if timestamp < cutoff_time:
                    symbols_to_remove.append(symbol)
            
            for symbol in symbols_to_remove:
                self.latest_data[exchange_name].pop(symbol, None)
                exchange_timestamps.pop(symbol, None)
                cleaned_count += 1
        
        if cleaned_count > 0:
            logger.debug(f"Cleaned up {cleaned_count} old data entries")
        
        self.last_cleanup_time = current_time
    
    # Abstract methods to be implemented by exchange-specific managers
    async def _subscribe_exchange_symbol(self, connection: BaseWebSocketManager, 
                                       exchange_name: str, symbol: str):
        """Subscribe to symbol on specific exchange."""
        raise NotImplementedError("Must be implemented by exchange-specific manager")
    
    async def _unsubscribe_exchange_symbol(self, connection: BaseWebSocketManager,
                                         exchange_name: str, symbol: str):
        """Unsubscribe from symbol on specific exchange."""
        raise NotImplementedError("Must be implemented by exchange-specific manager")
