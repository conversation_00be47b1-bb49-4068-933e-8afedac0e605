"""
Enhanced main entry point with WebSocket integration, advanced profit calculation, and risk management.
"""
import asyncio
import signal
import sys
import platform
from datetime import datetime, timedelta
from typing import Optional

from src.config.settings import config
from src.database.models import db_manager
from src.websocket.exchange_websocket_manager import exchange_websocket_manager
from src.arbitrage.realtime_engine import realtime_arbitrage_engine
from src.arbitrage.handlers import create_default_handler
from src.notifications.telegram_bot import telegram_notifier
from src.risk.manager import risk_manager
from src.utils.logger import logger


class EnhancedArbitrageBot:
    """Enhanced arbitrage bot with real-time WebSocket processing."""
    
    def __init__(self):
        self.is_running = False
        self.start_time: Optional[datetime] = None
        self.last_summary_time = datetime.now()
        self.last_cleanup_time = datetime.now()
        self.shutdown_event = asyncio.Event()
        
        # Performance tracking
        self.uptime_seconds = 0
        self.total_opportunities = 0
        self.successful_notifications = 0
        
    async def initialize(self) -> bool:
        """Initialize all bot components."""
        logger.info("Initializing enhanced arbitrage bot...")

        try:
            # Initialize database
            await db_manager.initialize()
            logger.info("Database initialized")

            # Initialize Telegram notifier
            await telegram_notifier.initialize()
            logger.info("Telegram notifier initialized")

            # Initialize real-time arbitrage engine
            success = await realtime_arbitrage_engine.start()
            if not success:
                logger.error("Failed to initialize real-time arbitrage engine")
                return False
            logger.info("Real-time arbitrage engine initialized")

            # Set up arbitrage handlers
            default_handler = create_default_handler()
            realtime_arbitrage_engine.add_handler(default_handler)
            logger.info("Arbitrage handlers configured")

            # Reset daily risk limits
            risk_manager.reset_daily_limits()
            logger.info("Risk management initialized")

            logger.info("Enhanced bot initialization complete")
            return True

        except Exception as e:
            logger.error(f"Error during initialization: {e}")
            return False
    
    async def run(self):
        """Main bot loop with real-time processing."""
        self.is_running = True
        self.start_time = datetime.now()
        logger.info("Enhanced arbitrage bot started with real-time WebSocket processing")
        
        if telegram_notifier.is_configured:
            await telegram_notifier.send_status_update(
                "Enhanced arbitrage bot started with real-time processing"
            )
        
        # Start periodic tasks
        periodic_task = asyncio.create_task(self._periodic_tasks_loop())
        
        try:
            # Wait for shutdown signal
            await self.shutdown_event.wait()
            
        except asyncio.CancelledError:
            logger.info("Bot loop cancelled")
        finally:
            # Cancel periodic tasks
            periodic_task.cancel()
            try:
                await periodic_task
            except asyncio.CancelledError:
                pass
    
    async def _periodic_tasks_loop(self):
        """Run periodic maintenance tasks."""
        while self.is_running:
            try:
                await asyncio.sleep(60)  # Run every minute
                
                # Update uptime
                if self.start_time:
                    self.uptime_seconds = (datetime.now() - self.start_time).total_seconds()
                
                # Periodic summary (every 30 minutes)
                if (datetime.now() - self.last_summary_time).total_seconds() >= 1800:
                    await self._send_periodic_summary()
                    self.last_summary_time = datetime.now()
                
                # Daily cleanup (every 24 hours)
                if (datetime.now() - self.last_cleanup_time).total_seconds() >= 86400:
                    await self._daily_cleanup()
                    self.last_cleanup_time = datetime.now()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in periodic tasks: {e}")
    
    async def _send_periodic_summary(self):
        """Send periodic performance summary."""
        try:
            # Get performance statistics
            engine_stats = realtime_arbitrage_engine.get_performance_stats()
            websocket_stats = exchange_websocket_manager.get_performance_stats()
            risk_stats = risk_manager.get_risk_statistics()
            
            # Create summary
            summary = {
                'uptime_hours': round(self.uptime_seconds / 3600, 1),
                'connected_exchanges': len(websocket_stats.get('connected_exchanges', [])),
                'total_opportunities': engine_stats['metrics']['total_opportunities'],
                'approved_opportunities': engine_stats['metrics']['approved_opportunities'],
                'rejected_opportunities': engine_stats['metrics']['rejected_opportunities'],
                'average_profit_percent': round(engine_stats['metrics']['average_profit_percent'], 2),
                'average_processing_time_ms': round(engine_stats['performance']['average_processing_time_ms'], 1),
                'daily_volume': risk_stats.get('daily_volume', 0),
                'websocket_messages': websocket_stats.get('total_messages', 0)
            }
            
            # Send to Telegram if configured
            if telegram_notifier.is_configured:
                await telegram_notifier.send_performance_summary(summary)
            
            logger.info(f"Periodic summary: {summary}")
            
        except Exception as e:
            logger.error(f"Error sending periodic summary: {e}")
    
    async def _daily_cleanup(self):
        """Perform daily cleanup tasks."""
        try:
            logger.info("Performing daily cleanup...")
            
            # Clean up old database records
            await db_manager.cleanup_old_data()
            
            # Reset daily risk limits
            risk_manager.reset_daily_limits()
            
            # Send daily summary
            if telegram_notifier.is_configured:
                daily_stats = await self._get_daily_statistics()
                await telegram_notifier.send_daily_summary(daily_stats)
            
            logger.info("Daily cleanup completed")
            
        except Exception as e:
            logger.error(f"Error in daily cleanup: {e}")
    
    async def _get_daily_statistics(self) -> dict:
        """Get daily statistics for reporting."""
        try:
            # Get signals from last 24 hours
            since = datetime.now() - timedelta(days=1)
            
            # This would need to be implemented in db_manager
            # For now, return basic stats
            engine_stats = realtime_arbitrage_engine.get_performance_stats()
            
            return {
                'date': datetime.now().strftime('%Y-%m-%d'),
                'total_opportunities': engine_stats['metrics']['total_opportunities'],
                'approved_opportunities': engine_stats['metrics']['approved_opportunities'],
                'rejected_opportunities': engine_stats['metrics']['rejected_opportunities'],
                'average_profit_percent': engine_stats['metrics']['average_profit_percent'],
                'uptime_hours': round(self.uptime_seconds / 3600, 1),
                'connected_exchanges': len(exchange_websocket_manager.get_connected_exchanges())
            }
            
        except Exception as e:
            logger.error(f"Error getting daily statistics: {e}")
            return {}
    
    async def shutdown(self):
        """Gracefully shut down the bot."""
        logger.info("Shutting down enhanced arbitrage bot...")
        self.is_running = False

        try:
            if telegram_notifier.is_configured:
                await telegram_notifier.send_status_update("Enhanced bot is shutting down")

            # Stop real-time engine
            await realtime_arbitrage_engine.stop()

            # Stop WebSocket manager
            await exchange_websocket_manager.stop()



            logger.info("Enhanced bot shutdown complete")

        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
        finally:
            self.shutdown_event.set()
    
    def get_status(self) -> dict:
        """Get current bot status."""
        return {
            'is_running': self.is_running,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'uptime_seconds': self.uptime_seconds,
            'connected_exchanges': len(exchange_websocket_manager.get_connected_exchanges()),
            'websocket_connected': len(exchange_websocket_manager.get_connected_exchanges()) > 0,
            'telegram_configured': telegram_notifier.is_configured,
            'realtime_engine_running': realtime_arbitrage_engine.is_running
        }


async def main():
    """Main function to run the enhanced bot."""
    bot = EnhancedArbitrageBot()
    
    # Set up signal handlers for graceful shutdown
    def signal_handler():
        logger.info("Shutdown signal received")
        asyncio.create_task(bot.shutdown())
    
    # Handle different signal types based on platform
    if platform.system() != "Windows":
        loop = asyncio.get_event_loop()
        for sig in (signal.SIGTERM, signal.SIGINT):
            loop.add_signal_handler(sig, signal_handler)
    else:
        # Windows doesn't support add_signal_handler
        signal.signal(signal.SIGINT, lambda s, f: signal_handler())
        signal.signal(signal.SIGTERM, lambda s, f: signal_handler())
    
    try:
        # Initialize bot
        success = await bot.initialize()
        if not success:
            logger.error("Failed to initialize bot")
            return 1
        
        # Run bot
        await bot.run()
        
        return 0
        
    except KeyboardInterrupt:
        logger.info("Keyboard interrupt received")
        await bot.shutdown()
        return 0
        
    except Exception as e:
        logger.critical(f"Critical error occurred: {e}")
        if telegram_notifier.is_configured:
            await telegram_notifier.send_error_notification(f"Critical error: {e}")
        return 1


if __name__ == "__main__":
    # Run the enhanced bot
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
