"""
Logging configuration for the arbitrage bot.
"""
import sys
from pathlib import Path
from loguru import logger as loguru_logger

from src.config.settings import config


def setup_logger():
    """Setup logger with configuration from settings."""
    # Remove default handler
    loguru_logger.remove()
    
    log_config = config.logging
    
    # Console handler
    if log_config.console_output:
        loguru_logger.add(
            sys.stdout,
            level=log_config.level,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
                   "<level>{level: <8}</level> | "
                   "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
                   "<level>{message}</level>",
            colorize=True
        )
    
    # File handler
    log_file = Path(log_config.file_path)
    log_file.parent.mkdir(parents=True, exist_ok=True)
    
    loguru_logger.add(
        log_file,
        level=log_config.level,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
        rotation=log_config.max_file_size,
        retention=log_config.backup_count,
        compression="zip"
    )
    
    return loguru_logger


# Initialize logger
logger = setup_logger()
