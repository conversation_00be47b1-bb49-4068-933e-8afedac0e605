#!/usr/bin/env python3
"""
Performance benchmark script to validate <100ms latency requirement and system optimization.
"""
import asyncio
import time
import statistics
import sys
from datetime import datetime
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.websocket.data_normalizer import NormalizedTickerData
from src.profit.calculator import ProfitCalculator
from src.risk.manager import RiskManager
from src.monitoring.performance_monitor import PerformanceMonitor
from src.arbitrage.realtime_engine import RealtimeArbitrageEngine


class PerformanceBenchmark:
    """Performance benchmark suite for arbitrage bot."""
    
    def __init__(self):
        self.results = {}
        self.monitor = PerformanceMonitor(alert_threshold_ms=100.0)
        
    async def run_all_benchmarks(self):
        """Run all performance benchmarks."""
        print("🚀 Starting Performance Benchmark Suite")
        print("=" * 50)
        
        # Start monitoring
        await self.monitor.start_monitoring(interval_seconds=0.1)
        
        try:
            # Run individual benchmarks
            await self.benchmark_profit_calculation()
            await self.benchmark_risk_assessment()
            await self.benchmark_data_normalization()
            await self.benchmark_arbitrage_detection()
            await self.benchmark_throughput()
            
            # Generate summary report
            self.generate_summary_report()
            
        finally:
            await self.monitor.stop_monitoring()
    
    async def benchmark_profit_calculation(self):
        """Benchmark profit calculation performance."""
        print("\n📊 Benchmarking Profit Calculation...")
        
        calculator = ProfitCalculator()
        latencies = []
        
        # Create test data
        buy_ticker = NormalizedTickerData(
            symbol="ADA/USDT",
            bid=0.4995,
            ask=0.5000,
            last=0.4998,
            volume=1000000,
            timestamp=datetime.now(),
            exchange="gate"
        )
        
        sell_ticker = NormalizedTickerData(
            symbol="ADA/USDT",
            bid=0.5020,
            ask=0.5025,
            last=0.5022,
            volume=800000,
            timestamp=datetime.now(),
            exchange="mexc"
        )
        
        # Run multiple iterations
        iterations = 100
        for i in range(iterations):
            start_time = time.perf_counter()
            
            try:
                profit_calc = calculator.calculate_profit(
                    buy_ticker=buy_ticker,
                    sell_ticker=sell_ticker,
                    trade_amount_usd=100.0
                )
                success = profit_calc is not None
            except Exception as e:
                print(f"Error in iteration {i}: {e}")
                success = False
            
            end_time = time.perf_counter()
            latency_ms = (end_time - start_time) * 1000
            latencies.append(latency_ms)
            
            if not success:
                print(f"❌ Failed iteration {i}")
        
        # Calculate statistics
        avg_latency = statistics.mean(latencies)
        p95_latency = sorted(latencies)[int(0.95 * len(latencies))]
        max_latency = max(latencies)
        min_latency = min(latencies)
        
        self.results['profit_calculation'] = {
            'avg_latency_ms': avg_latency,
            'p95_latency_ms': p95_latency,
            'max_latency_ms': max_latency,
            'min_latency_ms': min_latency,
            'iterations': iterations,
            'meets_requirement': avg_latency < 100 and p95_latency < 100
        }
        
        print(f"  Average Latency: {avg_latency:.2f}ms")
        print(f"  P95 Latency: {p95_latency:.2f}ms")
        print(f"  Max Latency: {max_latency:.2f}ms")
        print(f"  Min Latency: {min_latency:.2f}ms")
        print(f"  ✅ Meets <100ms requirement: {self.results['profit_calculation']['meets_requirement']}")
    
    async def benchmark_risk_assessment(self):
        """Benchmark risk assessment performance."""
        print("\n⚠️  Benchmarking Risk Assessment...")
        
        risk_manager = RiskManager()
        latencies = []
        
        # Create mock data
        from unittest.mock import MagicMock
        
        profit_calc = MagicMock()
        profit_calc.symbol = "ADA/USDT"
        profit_calc.buy_exchange = "gate"
        profit_calc.sell_exchange = "mexc"
        profit_calc.profit_percent = 2.0
        profit_calc.is_profitable = True
        profit_calc.calculation_time = datetime.now()
        profit_calc.risk_score = 0.3
        profit_calc.confidence = 0.8
        profit_calc.total_fees = 1.0
        profit_calc.total_slippage = 0.1
        profit_calc.trade_amount = 200.0
        profit_calc.buy_price = 0.5000
        
        ticker_data = [
            NormalizedTickerData(
                symbol="ADA/USDT",
                bid=0.4995,
                ask=0.5000,
                last=0.4998,
                volume=1000000,
                timestamp=datetime.now(),
                exchange="gate"
            )
        ]
        
        # Run multiple iterations
        iterations = 50
        for i in range(iterations):
            start_time = time.perf_counter()
            
            try:
                assessment = await risk_manager.assess_risk(
                    profit_calc=profit_calc,
                    ticker_data=ticker_data,
                    trade_amount_usd=100.0
                )
                success = assessment is not None
            except Exception as e:
                print(f"Error in iteration {i}: {e}")
                success = False
            
            end_time = time.perf_counter()
            latency_ms = (end_time - start_time) * 1000
            latencies.append(latency_ms)
            
            if not success:
                print(f"❌ Failed iteration {i}")
        
        # Calculate statistics
        avg_latency = statistics.mean(latencies)
        p95_latency = sorted(latencies)[int(0.95 * len(latencies))]
        max_latency = max(latencies)
        
        self.results['risk_assessment'] = {
            'avg_latency_ms': avg_latency,
            'p95_latency_ms': p95_latency,
            'max_latency_ms': max_latency,
            'iterations': iterations,
            'meets_requirement': avg_latency < 50 and p95_latency < 100
        }
        
        print(f"  Average Latency: {avg_latency:.2f}ms")
        print(f"  P95 Latency: {p95_latency:.2f}ms")
        print(f"  Max Latency: {max_latency:.2f}ms")
        print(f"  ✅ Meets <50ms target: {self.results['risk_assessment']['meets_requirement']}")
    
    async def benchmark_data_normalization(self):
        """Benchmark data normalization performance."""
        print("\n🔄 Benchmarking Data Normalization...")
        
        from src.websocket.data_normalizer import DataNormalizer
        
        latencies = []
        
        # Test data for different exchanges
        gate_data = {
            "method": "ticker.update",
            "params": [
                "ADA_USDT",
                {
                    "highest_bid": "0.5000",
                    "lowest_ask": "0.5010",
                    "last": "0.5005",
                    "base_volume": "1000000",
                    "high_24h": "0.5100",
                    "low_24h": "0.4900"
                }
            ]
        }
        
        # Run multiple iterations
        iterations = 1000
        for i in range(iterations):
            start_time = time.perf_counter()
            
            try:
                normalized = DataNormalizer.normalize_gate_ticker(gate_data, "ADA/USDT")
                success = normalized is not None
            except Exception as e:
                print(f"Error in iteration {i}: {e}")
                success = False
            
            end_time = time.perf_counter()
            latency_ms = (end_time - start_time) * 1000
            latencies.append(latency_ms)
            
            if not success:
                print(f"❌ Failed iteration {i}")
        
        # Calculate statistics
        avg_latency = statistics.mean(latencies)
        p95_latency = sorted(latencies)[int(0.95 * len(latencies))]
        throughput = 1000 / avg_latency  # messages per second
        
        self.results['data_normalization'] = {
            'avg_latency_ms': avg_latency,
            'p95_latency_ms': p95_latency,
            'throughput_msg_per_sec': throughput,
            'iterations': iterations,
            'meets_requirement': avg_latency < 10  # Should be very fast
        }
        
        print(f"  Average Latency: {avg_latency:.3f}ms")
        print(f"  P95 Latency: {p95_latency:.3f}ms")
        print(f"  Throughput: {throughput:.0f} msg/sec")
        print(f"  ✅ Meets <10ms target: {self.results['data_normalization']['meets_requirement']}")
    
    async def benchmark_arbitrage_detection(self):
        """Benchmark end-to-end arbitrage detection."""
        print("\n🎯 Benchmarking Arbitrage Detection...")
        
        engine = RealtimeArbitrageEngine()
        latencies = []
        
        # Create test ticker data
        ticker_data = [
            NormalizedTickerData(
                symbol="ADA/USDT",
                bid=0.4995,
                ask=0.5000,
                last=0.4998,
                volume=1000000,
                timestamp=datetime.now(),
                exchange="gate"
            ),
            NormalizedTickerData(
                symbol="ADA/USDT",
                bid=0.5020,
                ask=0.5025,
                last=0.5022,
                volume=800000,
                timestamp=datetime.now(),
                exchange="mexc"
            )
        ]
        
        # Run multiple iterations
        iterations = 50
        for i in range(iterations):
            start_time = time.perf_counter()
            
            try:
                opportunities = await engine._find_opportunities(ticker_data)
                success = True
            except Exception as e:
                print(f"Error in iteration {i}: {e}")
                success = False
                opportunities = []
            
            end_time = time.perf_counter()
            latency_ms = (end_time - start_time) * 1000
            latencies.append(latency_ms)
            
            if not success:
                print(f"❌ Failed iteration {i}")
        
        # Calculate statistics
        avg_latency = statistics.mean(latencies)
        p95_latency = sorted(latencies)[int(0.95 * len(latencies))]
        max_latency = max(latencies)
        
        self.results['arbitrage_detection'] = {
            'avg_latency_ms': avg_latency,
            'p95_latency_ms': p95_latency,
            'max_latency_ms': max_latency,
            'iterations': iterations,
            'meets_requirement': avg_latency < 100 and p95_latency < 100
        }
        
        print(f"  Average Latency: {avg_latency:.2f}ms")
        print(f"  P95 Latency: {p95_latency:.2f}ms")
        print(f"  Max Latency: {max_latency:.2f}ms")
        print(f"  ✅ Meets <100ms requirement: {self.results['arbitrage_detection']['meets_requirement']}")
    
    async def benchmark_throughput(self):
        """Benchmark overall system throughput."""
        print("\n🚄 Benchmarking System Throughput...")
        
        # Simulate high-frequency message processing
        message_count = 1000
        start_time = time.perf_counter()
        
        for i in range(message_count):
            async with self.monitor.measure_latency("throughput_test"):
                # Simulate message processing
                await asyncio.sleep(0.001)  # 1ms per message
        
        end_time = time.perf_counter()
        total_time = end_time - start_time
        throughput = message_count / total_time
        
        self.results['throughput'] = {
            'messages_processed': message_count,
            'total_time_seconds': total_time,
            'throughput_msg_per_sec': throughput,
            'meets_requirement': throughput > 100  # Target >100 msg/sec
        }
        
        print(f"  Messages Processed: {message_count}")
        print(f"  Total Time: {total_time:.2f}s")
        print(f"  Throughput: {throughput:.1f} msg/sec")
        print(f"  ✅ Meets >100 msg/sec target: {self.results['throughput']['meets_requirement']}")
    
    def generate_summary_report(self):
        """Generate comprehensive performance summary report."""
        print("\n" + "=" * 50)
        print("📋 PERFORMANCE BENCHMARK SUMMARY")
        print("=" * 50)
        
        all_passed = True
        
        for test_name, results in self.results.items():
            status = "✅ PASS" if results['meets_requirement'] else "❌ FAIL"
            print(f"{test_name.replace('_', ' ').title()}: {status}")
            
            if not results['meets_requirement']:
                all_passed = False
        
        print("\n" + "=" * 50)
        overall_status = "✅ ALL TESTS PASSED" if all_passed else "❌ SOME TESTS FAILED"
        print(f"OVERALL RESULT: {overall_status}")
        print("=" * 50)
        
        # Detailed results
        print("\n📊 DETAILED RESULTS:")
        for test_name, results in self.results.items():
            print(f"\n{test_name.replace('_', ' ').title()}:")
            for key, value in results.items():
                if isinstance(value, float):
                    print(f"  {key}: {value:.3f}")
                else:
                    print(f"  {key}: {value}")
        
        return all_passed


async def main():
    """Run performance benchmark."""
    benchmark = PerformanceBenchmark()
    
    try:
        success = await benchmark.run_all_benchmarks()
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n⚠️  Benchmark interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Benchmark failed with error: {e}")
        return 1


if __name__ == "__main__":
    import sys
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
