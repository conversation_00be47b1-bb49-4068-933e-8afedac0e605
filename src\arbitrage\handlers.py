"""
Arbitrage opportunity handlers for notifications and actions.
"""
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional

from src.database.models import ArbitrageSignal, db_manager
from src.notifications.telegram_bot import telegram_notifier
from src.risk.manager import <PERSON><PERSON>sessment, RiskDecision, RiskLevel
from src.config.settings import config
from src.utils.logger import logger


class TelegramNotificationHandler:
    """Handler for sending arbitrage opportunities to Telegram."""
    
    def __init__(self):
        self.last_notifications: Dict[str, datetime] = {}
        self.notification_count = 0
        
    async def handle_opportunity(self, signal: ArbitrageSignal, 
                               risk_assessment: RiskAssessment) -> bool:
        """Handle arbitrage opportunity by sending Telegram notification."""
        try:
            # Check if we should send notification
            if not self._should_notify(signal, risk_assessment):
                return False
            
            # Send notification
            success = await telegram_notifier.send_arbitrage_signal(signal)
            
            if success:
                self.notification_count += 1
                self.last_notifications[signal.symbol] = datetime.now()
                
                # Update signal status in database
                if signal.id:
                    await db_manager.update_signal_telegram_status(signal.id, True)
                
                logger.info(f"Sent Telegram notification for {signal.symbol}")
                return True
            else:
                logger.warning(f"Failed to send Telegram notification for {signal.symbol}")
                return False
                
        except Exception as e:
            logger.error(f"Error in Telegram notification handler: {e}")
            return False
    
    def _should_notify(self, signal: ArbitrageSignal, risk_assessment: RiskAssessment) -> bool:
        """Check if we should send a notification for this signal."""
        # Don't notify if Telegram is not configured
        if not telegram_notifier.is_configured:
            return False
        
        # Don't notify rejected opportunities
        if risk_assessment.decision == RiskDecision.REJECT:
            return False
        
        # Don't notify critical risk opportunities
        if risk_assessment.risk_level == RiskLevel.CRITICAL:
            return False
        
        # Check minimum profit threshold
        min_profit = self._get_min_profit_for_symbol(signal.symbol)
        if signal.profit_percent < min_profit:
            return False
        
        # Check notification frequency
        last_notification = self.last_notifications.get(signal.symbol)
        if last_notification:
            time_since_last = (datetime.now() - last_notification).total_seconds()
            if time_since_last < config.bot.max_signal_frequency:
                return False
        
        return True
    
    def _get_min_profit_for_symbol(self, symbol: str) -> float:
        """Get minimum profit threshold for a specific symbol."""
        trading_pairs = config.trading_pairs
        
        for pair in trading_pairs:
            if pair.symbol == symbol:
                return pair.min_profit
        
        return config.bot.profit_threshold
    
    def get_stats(self) -> Dict[str, any]:
        """Get notification handler statistics."""
        return {
            'notification_count': self.notification_count,
            'symbols_notified': len(self.last_notifications),
            'last_notifications': {
                symbol: timestamp.isoformat()
                for symbol, timestamp in self.last_notifications.items()
            }
        }


class DatabaseLogHandler:
    """Handler for logging arbitrage opportunities to database."""
    
    def __init__(self):
        self.logged_count = 0
        
    async def handle_opportunity(self, signal: ArbitrageSignal, 
                               risk_assessment: RiskAssessment) -> bool:
        """Handle arbitrage opportunity by logging to database."""
        try:
            # The signal should already be stored by the engine, but we can add
            # additional logging or update with risk assessment data
            
            if signal.id:
                # Could store risk assessment data in a separate table
                # For now, just log the event
                logger.info(
                    f"Logged arbitrage opportunity: {signal.symbol} "
                    f"Profit: {signal.profit_percent:.2f}% "
                    f"Risk: {risk_assessment.risk_level.value}"
                )
                
                self.logged_count += 1
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error in database log handler: {e}")
            return False
    
    def get_stats(self) -> Dict[str, any]:
        """Get database handler statistics."""
        return {
            'logged_count': self.logged_count
        }


class RiskFilterHandler:
    """Handler that filters opportunities based on risk criteria."""
    
    def __init__(self, max_risk_score: float = 0.7, 
                 min_confidence: float = 0.6):
        self.max_risk_score = max_risk_score
        self.min_confidence = min_confidence
        self.filtered_count = 0
        self.passed_count = 0
        
    async def handle_opportunity(self, signal: ArbitrageSignal, 
                               risk_assessment: RiskAssessment) -> bool:
        """Handle arbitrage opportunity by applying risk filters."""
        try:
            # Apply risk filters
            if risk_assessment.risk_score > self.max_risk_score:
                self.filtered_count += 1
                logger.debug(
                    f"Filtered opportunity {signal.symbol}: "
                    f"Risk score {risk_assessment.risk_score:.2f} > {self.max_risk_score}"
                )
                return False
            
            if risk_assessment.confidence < self.min_confidence:
                self.filtered_count += 1
                logger.debug(
                    f"Filtered opportunity {signal.symbol}: "
                    f"Confidence {risk_assessment.confidence:.2f} < {self.min_confidence}"
                )
                return False
            
            # Opportunity passed filters
            self.passed_count += 1
            logger.debug(f"Opportunity {signal.symbol} passed risk filters")
            return True
            
        except Exception as e:
            logger.error(f"Error in risk filter handler: {e}")
            return False
    
    def get_stats(self) -> Dict[str, any]:
        """Get risk filter handler statistics."""
        total = self.filtered_count + self.passed_count
        pass_rate = (self.passed_count / total * 100) if total > 0 else 0
        
        return {
            'filtered_count': self.filtered_count,
            'passed_count': self.passed_count,
            'pass_rate_percent': pass_rate,
            'max_risk_score': self.max_risk_score,
            'min_confidence': self.min_confidence
        }


class CompositeHandler:
    """Composite handler that chains multiple handlers."""
    
    def __init__(self, handlers: List = None):
        self.handlers = handlers or []
        self.execution_stats: Dict[str, Dict] = {}
        
    def add_handler(self, handler):
        """Add a handler to the chain."""
        self.handlers.append(handler)
        
    async def handle_opportunity(self, signal: ArbitrageSignal, 
                               risk_assessment: RiskAssessment) -> bool:
        """Handle opportunity by executing all handlers in sequence."""
        results = []
        
        for handler in self.handlers:
            try:
                start_time = datetime.now()
                result = await handler.handle_opportunity(signal, risk_assessment)
                execution_time = (datetime.now() - start_time).total_seconds() * 1000  # ms
                
                # Track execution stats
                handler_name = handler.__class__.__name__
                if handler_name not in self.execution_stats:
                    self.execution_stats[handler_name] = {
                        'executions': 0,
                        'successes': 0,
                        'failures': 0,
                        'total_time_ms': 0,
                        'average_time_ms': 0
                    }
                
                stats = self.execution_stats[handler_name]
                stats['executions'] += 1
                stats['total_time_ms'] += execution_time
                stats['average_time_ms'] = stats['total_time_ms'] / stats['executions']
                
                if result:
                    stats['successes'] += 1
                else:
                    stats['failures'] += 1
                
                results.append(result)
                
            except Exception as e:
                logger.error(f"Error in handler {handler.__class__.__name__}: {e}")
                results.append(False)
        
        # Return True if at least one handler succeeded
        return any(results)
    
    def get_stats(self) -> Dict[str, any]:
        """Get composite handler statistics."""
        handler_stats = {}
        
        for handler in self.handlers:
            if hasattr(handler, 'get_stats'):
                handler_stats[handler.__class__.__name__] = handler.get_stats()
        
        return {
            'handlers_count': len(self.handlers),
            'execution_stats': dict(self.execution_stats),
            'handler_stats': handler_stats
        }


def create_default_handler() -> CompositeHandler:
    """Create default composite handler with standard handlers."""
    composite = CompositeHandler()
    
    # Add risk filter first
    composite.add_handler(RiskFilterHandler(
        max_risk_score=0.7,
        min_confidence=0.6
    ))
    
    # Add database logging
    composite.add_handler(DatabaseLogHandler())
    
    # Add Telegram notifications
    composite.add_handler(TelegramNotificationHandler())
    
    return composite
