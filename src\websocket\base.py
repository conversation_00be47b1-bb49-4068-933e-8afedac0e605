"""
Base WebSocket connection manager with automatic reconnection and heartbeat support.
"""
import asyncio
import json
import time
import random
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, Any, Optional, Callable, List
from enum import Enum
import websockets
from websockets.exceptions import ConnectionClosed, WebSocketException

from src.utils.logger import logger


class ConnectionState(Enum):
    """WebSocket connection states."""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    RECONNECTING = "reconnecting"
    FAILED = "failed"


class WebSocketConfig:
    """Configuration for WebSocket connections."""
    
    def __init__(
        self,
        url: str,
        ping_interval: int = 30,
        ping_timeout: int = 10,
        max_reconnect_attempts: int = 10,
        initial_reconnect_delay: float = 1.0,
        max_reconnect_delay: float = 60.0,
        reconnect_backoff_factor: float = 2.0,
        connection_timeout: int = 10,
        message_timeout: int = 5
    ):
        self.url = url
        self.ping_interval = ping_interval
        self.ping_timeout = ping_timeout
        self.max_reconnect_attempts = max_reconnect_attempts
        self.initial_reconnect_delay = initial_reconnect_delay
        self.max_reconnect_delay = max_reconnect_delay
        self.reconnect_backoff_factor = reconnect_backoff_factor
        self.connection_timeout = connection_timeout
        self.message_timeout = message_timeout


class BaseWebSocketManager(ABC):
    """
    Base WebSocket manager with automatic reconnection, heartbeat, and error handling.
    """
    
    def __init__(self, exchange_name: str, config: WebSocketConfig):
        self.exchange_name = exchange_name
        self.config = config
        self.websocket: Optional[websockets.WebSocketServerProtocol] = None
        self.state = ConnectionState.DISCONNECTED
        
        # Connection management
        self.reconnect_attempts = 0
        self.last_ping_time = 0
        self.last_pong_time = 0
        self.connection_start_time = 0
        
        # Event handlers
        self.message_handlers: Dict[str, Callable] = {}
        self.error_handlers: List[Callable] = []
        self.connection_handlers: List[Callable] = []
        self.disconnection_handlers: List[Callable] = []
        
        # Tasks
        self.connection_task: Optional[asyncio.Task] = None
        self.heartbeat_task: Optional[asyncio.Task] = None
        self.message_task: Optional[asyncio.Task] = None
        
        # Statistics
        self.messages_received = 0
        self.messages_sent = 0
        self.connection_count = 0
        self.last_message_time = 0
        
        # Shutdown flag
        self.is_shutdown = False
    
    async def start(self) -> bool:
        """Start the WebSocket connection."""
        if self.state != ConnectionState.DISCONNECTED:
            logger.warning(f"{self.exchange_name}: WebSocket already started")
            return False
        
        self.is_shutdown = False
        self.connection_task = asyncio.create_task(self._connection_loop())
        
        # Wait for initial connection
        for _ in range(50):  # Wait up to 5 seconds
            if self.state == ConnectionState.CONNECTED:
                return True
            await asyncio.sleep(0.1)
        
        logger.error(f"{self.exchange_name}: Failed to establish initial connection")
        return False
    
    async def stop(self):
        """Stop the WebSocket connection and cleanup."""
        self.is_shutdown = True
        self.state = ConnectionState.DISCONNECTED
        
        # Cancel all tasks
        tasks = [self.connection_task, self.heartbeat_task, self.message_task]
        for task in tasks:
            if task and not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
        
        # Close WebSocket connection
        if self.websocket:
            try:
                await self.websocket.close()
            except Exception as e:
                logger.debug(f"{self.exchange_name}: Error closing WebSocket: {e}")
            finally:
                self.websocket = None
        
        logger.info(f"{self.exchange_name}: WebSocket stopped")
    
    async def send_message(self, message: Dict[str, Any]) -> bool:
        """Send a message through the WebSocket."""
        if self.state != ConnectionState.CONNECTED or not self.websocket:
            logger.warning(f"{self.exchange_name}: Cannot send message - not connected")
            return False
        
        try:
            message_str = json.dumps(message)
            await asyncio.wait_for(
                self.websocket.send(message_str),
                timeout=self.config.message_timeout
            )
            self.messages_sent += 1
            logger.debug(f"{self.exchange_name}: Sent message: {message_str}")
            return True
            
        except Exception as e:
            logger.error(f"{self.exchange_name}: Failed to send message: {e}")
            return False
    
    def add_message_handler(self, message_type: str, handler: Callable):
        """Add a message handler for specific message types."""
        self.message_handlers[message_type] = handler
    
    def add_error_handler(self, handler: Callable):
        """Add an error handler."""
        self.error_handlers.append(handler)
    
    def add_connection_handler(self, handler: Callable):
        """Add a connection event handler."""
        self.connection_handlers.append(handler)
    
    def add_disconnection_handler(self, handler: Callable):
        """Add a disconnection event handler."""
        self.disconnection_handlers.append(handler)
    
    async def _connection_loop(self):
        """Main connection loop with automatic reconnection."""
        while not self.is_shutdown:
            try:
                await self._connect()
                
                if self.state == ConnectionState.CONNECTED:
                    # Start heartbeat and message handling
                    self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())
                    self.message_task = asyncio.create_task(self._message_loop())
                    
                    # Wait for tasks to complete (usually due to disconnection)
                    await asyncio.gather(
                        self.heartbeat_task,
                        self.message_task,
                        return_exceptions=True
                    )
                
            except Exception as e:
                logger.error(f"{self.exchange_name}: Connection loop error: {e}")
                await self._handle_error(e)
            
            # Handle reconnection
            if not self.is_shutdown:
                await self._handle_reconnection()
    
    async def _connect(self):
        """Establish WebSocket connection."""
        self.state = ConnectionState.CONNECTING
        logger.info(f"{self.exchange_name}: Connecting to {self.config.url}")

        try:
            # Add random jitter to avoid thundering herd
            jitter = random.uniform(0.5, 2.0)  # Increased jitter range
            await asyncio.sleep(jitter)

            # More robust connection with additional parameters
            self.websocket = await asyncio.wait_for(
                websockets.connect(
                    self.config.url,
                    ping_interval=self.config.ping_interval,
                    ping_timeout=self.config.ping_timeout,
                    close_timeout=15,  # Longer close timeout
                    max_size=2**20,    # 1MB max message size
                    max_queue=32,      # Larger message queue
                    compression=None,  # Disable compression for stability
                    user_agent_header="ArbitrageBot/1.0"
                ),
                timeout=self.config.connection_timeout
            )

            self.state = ConnectionState.CONNECTED
            self.connection_start_time = time.time()
            self.connection_count += 1
            self.reconnect_attempts = 0

            logger.info(f"{self.exchange_name}: WebSocket connected successfully")

            # Call connection handlers
            for handler in self.connection_handlers:
                try:
                    await handler()
                except Exception as e:
                    logger.error(f"{self.exchange_name}: Connection handler error: {e}")

            # Send initial subscription messages
            await self._on_connected()

        except Exception as e:
            self.state = ConnectionState.FAILED
            logger.error(f"{self.exchange_name}: Connection failed: {e}")
            raise
    
    async def _heartbeat_loop(self):
        """Heartbeat loop to maintain connection."""
        consecutive_ping_failures = 0
        max_ping_failures = 3

        while self.state == ConnectionState.CONNECTED and not self.is_shutdown:
            try:
                current_time = time.time()

                # Send ping if needed
                if current_time - self.last_ping_time >= self.config.ping_interval:
                    try:
                        await self._send_ping()
                        self.last_ping_time = current_time
                        consecutive_ping_failures = 0  # Reset on successful ping
                    except Exception as ping_error:
                        consecutive_ping_failures += 1
                        logger.warning(f"{self.exchange_name}: Ping failed ({consecutive_ping_failures}/{max_ping_failures}): {ping_error}")

                        if consecutive_ping_failures >= max_ping_failures:
                            logger.error(f"{self.exchange_name}: Max ping failures reached, disconnecting")
                            break

                # Check for pong timeout - more lenient approach
                if (self.last_ping_time > 0 and
                    current_time - self.last_ping_time > self.config.ping_timeout * 2 and  # Double timeout for more tolerance
                    self.last_pong_time < self.last_ping_time):

                    logger.warning(f"{self.exchange_name}: Extended ping timeout detected, attempting reconnection")
                    break

                await asyncio.sleep(2)  # Check less frequently to reduce load

            except Exception as e:
                logger.error(f"{self.exchange_name}: Heartbeat error: {e}")
                break
    
    async def _message_loop(self):
        """Message receiving loop."""
        while self.state == ConnectionState.CONNECTED and not self.is_shutdown:
            try:
                if not self.websocket:
                    break
                
                message = await self.websocket.recv()
                self.messages_received += 1
                self.last_message_time = time.time()
                
                await self._handle_message(message)
                
            except ConnectionClosed:
                logger.info(f"{self.exchange_name}: WebSocket connection closed")
                break
            except WebSocketException as e:
                logger.error(f"{self.exchange_name}: WebSocket error: {e}")
                break
            except Exception as e:
                logger.error(f"{self.exchange_name}: Message loop error: {e}")
                await self._handle_error(e)
    
    async def _handle_message(self, message: str):
        """Handle incoming WebSocket message."""
        try:
            data = json.loads(message)
            logger.debug(f"{self.exchange_name}: Received message: {message}")
            
            # Handle pong messages
            if self._is_pong_message(data):
                self.last_pong_time = time.time()
                return
            
            # Route message to appropriate handler
            message_type = self._get_message_type(data)
            if message_type in self.message_handlers:
                await self.message_handlers[message_type](data)
            else:
                await self._on_message(data)
                
        except json.JSONDecodeError as e:
            logger.error(f"{self.exchange_name}: Invalid JSON message: {e}")
        except Exception as e:
            logger.error(f"{self.exchange_name}: Message handling error: {e}")
            await self._handle_error(e)
    
    async def _handle_reconnection(self):
        """Handle reconnection logic with exponential backoff."""
        if self.is_shutdown:
            return
        
        self.reconnect_attempts += 1
        
        if self.reconnect_attempts > self.config.max_reconnect_attempts:
            logger.error(f"{self.exchange_name}: Max reconnection attempts reached")
            self.state = ConnectionState.FAILED
            return
        
        # Calculate backoff delay
        delay = min(
            self.config.initial_reconnect_delay * (
                self.config.reconnect_backoff_factor ** (self.reconnect_attempts - 1)
            ),
            self.config.max_reconnect_delay
        )
        
        # Add jitter to prevent thundering herd
        jitter = random.uniform(0.8, 1.2)
        delay *= jitter
        
        logger.info(
            f"{self.exchange_name}: Reconnecting in {delay:.1f}s "
            f"(attempt {self.reconnect_attempts}/{self.config.max_reconnect_attempts})"
        )
        
        self.state = ConnectionState.RECONNECTING
        await asyncio.sleep(delay)
    
    async def _handle_error(self, error: Exception):
        """Handle errors and notify error handlers."""
        for handler in self.error_handlers:
            try:
                await handler(error)
            except Exception as e:
                logger.error(f"{self.exchange_name}: Error handler failed: {e}")
    
    # Abstract methods to be implemented by subclasses
    @abstractmethod
    async def _on_connected(self):
        """Called when WebSocket connection is established."""
        pass
    
    @abstractmethod
    async def _on_message(self, data: Dict[str, Any]):
        """Called when a message is received."""
        pass
    
    @abstractmethod
    async def _send_ping(self):
        """Send ping message to maintain connection."""
        pass
    
    @abstractmethod
    def _is_pong_message(self, data: Dict[str, Any]) -> bool:
        """Check if message is a pong response."""
        pass
    
    @abstractmethod
    def _get_message_type(self, data: Dict[str, Any]) -> str:
        """Extract message type from received data."""
        pass
    
    def get_stats(self) -> Dict[str, Any]:
        """Get connection statistics."""
        current_time = time.time()
        return {
            "exchange": self.exchange_name,
            "state": self.state.value,
            "connection_count": self.connection_count,
            "reconnect_attempts": self.reconnect_attempts,
            "messages_received": self.messages_received,
            "messages_sent": self.messages_sent,
            "uptime": current_time - self.connection_start_time if self.connection_start_time else 0,
            "last_message_age": current_time - self.last_message_time if self.last_message_time else None,
            "is_connected": self.state == ConnectionState.CONNECTED
        }
