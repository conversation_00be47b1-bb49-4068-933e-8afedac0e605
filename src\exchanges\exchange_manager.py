"""
Exchange manager for handling connections to multiple cryptocurrency exchanges.
"""
import asyncio
import ccxt.async_support as ccxt
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from asyncio_throttle import Throttler
import aiohttp
import random
import time

from src.config.settings import config
from src.database.models import Price<PERSON><PERSON>, db_manager
from src.exchanges.mock_exchange import MockExchangeConnector
from src.utils.logger import logger


class AntiDetectionManager:
    """Manages anti-detection strategies to avoid rate limiting and IP bans."""

    @staticmethod
    def get_random_user_agent() -> str:
        """Get a random realistic user agent."""
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/121.0',
        ]
        return random.choice(user_agents)

    @staticmethod
    def get_realistic_headers() -> dict:
        """Get realistic browser headers."""
        return {
            'User-Agent': AntiDetectionManager.get_random_user_agent(),
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9,tr;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        }

    @staticmethod
    async def add_random_delay(min_delay: float = 0.5, max_delay: float = 2.0):
        """Add random delay to mimic human behavior."""
        delay = random.uniform(min_delay, max_delay)
        await asyncio.sleep(delay)

    @staticmethod
    def get_conservative_rate_limit(base_rate: int) -> int:
        """Get a more conservative rate limit to avoid detection."""
        # Reduce rate limit by 30-50% to be safer
        reduction_factor = random.uniform(0.5, 0.7)
        return int(base_rate * reduction_factor)


class ExchangeConnector:
    """Base connector for exchange operations."""
    
    def __init__(self, exchange_name: str, exchange_config: dict):
        self.name = exchange_name
        self.config = exchange_config
        self.exchange = None
        self.throttler = None
        self.is_connected = False
        self.last_error = None
        self.session = None  # Track aiohttp session for proper cleanup
        self.last_request_time = 0  # Track last request time for additional throttling
        self.request_count = 0  # Track request count for monitoring
        
    async def initialize(self):
        """Initialize exchange connection."""
        # Get credentials from config
        credentials = config.get_exchange_credentials(self.name)

        # Map exchange names to CCXT names if needed
        ccxt_name = self._get_ccxt_exchange_name(self.name)

        # Create exchange instance
        if not hasattr(ccxt, ccxt_name):
            raise Exception(f"Exchange {ccxt_name} not supported by CCXT")

        exchange_class = getattr(ccxt, ccxt_name)

        # Create aiohttp session with anti-detection settings
        connector = aiohttp.TCPConnector(
            ssl=True,  # Enable SSL for better legitimacy
            limit=20,  # Reduce connection pool size to be less aggressive
            limit_per_host=5,  # Much lower per-host limit
            ttl_dns_cache=600,  # Longer DNS cache
            use_dns_cache=True,
            keepalive_timeout=30,  # Keep connections alive longer
            enable_cleanup_closed=True,
        )

        # Create session with realistic timeout and headers
        timeout = aiohttp.ClientTimeout(
            total=self.config.timeout * 2,  # Double the timeout to be less aggressive
            connect=10,
            sock_read=10
        )

        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers=AntiDetectionManager.get_realistic_headers()
        )

        # Configure exchange with conservative, realistic settings
        exchange_config = {
            'timeout': self.config.timeout * 2000,  # Much longer timeout (double + ms conversion)
            'enableRateLimit': True,
            'rateLimit': AntiDetectionManager.get_conservative_rate_limit(self.config.rate_limit),
            'sandbox': False,
            'headers': AntiDetectionManager.get_realistic_headers(),
            'session': self.session,
            # Additional anti-detection settings
            'verbose': False,  # Disable verbose logging
            'userAgent': AntiDetectionManager.get_random_user_agent(),
        }

        # Add credentials if available
        if credentials['apiKey'] and credentials['secret']:
            exchange_config.update({
                'apiKey': credentials['apiKey'],
                'secret': credentials['secret'],
            })
            logger.info(f"Initialized {self.name} with API credentials")
        else:
            logger.info(f"Initialized {self.name} with public API only")

        # Try different hostnames if configured
        api_urls = self.config.api_urls if hasattr(self.config, 'api_urls') else []

        # If no custom URLs, try with default configuration first
        if not api_urls:
            try:
                self.exchange = exchange_class(exchange_config)

                # Set up conservative rate limiting
                conservative_rate = AntiDetectionManager.get_conservative_rate_limit(self.config.rate_limit)
                rate_limit_per_second = conservative_rate / 60  # Convert per minute to per second
                self.throttler = Throttler(rate_limit=rate_limit_per_second)
                logger.info(f"Set conservative rate limit for {self.name}: {conservative_rate}/min ({rate_limit_per_second:.2f}/sec)")

                # Test connection
                await self._test_connection()
                self.is_connected = True

                await db_manager.update_exchange_status(self.name, "CONNECTED")
                logger.info(f"Successfully connected to {self.name} using default configuration")
                return

            except Exception as e:
                logger.warning(f"Default configuration failed for {self.name}: {e}")
                if self.exchange:
                    await self.exchange.close()
                # Don't close session here as we'll reuse it for custom URLs

        # Try with custom hostnames
        for i, url in enumerate(api_urls):
            try:
                # Create a copy of the config for this attempt
                current_config = exchange_config.copy()

                # Set custom hostname
                hostname = url.replace('https://', '').replace('http://', '')
                current_config['hostname'] = hostname

                self.exchange = exchange_class(current_config)

                # Set up conservative rate limiting
                conservative_rate = AntiDetectionManager.get_conservative_rate_limit(self.config.rate_limit)
                rate_limit_per_second = conservative_rate / 60  # Convert per minute to per second
                self.throttler = Throttler(rate_limit=rate_limit_per_second)
                logger.info(f"Set conservative rate limit for {self.name}: {conservative_rate}/min ({rate_limit_per_second:.2f}/sec)")

                # Test connection
                await self._test_connection()
                self.is_connected = True

                await db_manager.update_exchange_status(self.name, "CONNECTED")
                logger.info(f"Successfully connected to {self.name} using {url}")
                return

            except Exception as e:
                logger.warning(f"Attempt {i+1} failed for {self.name} with url {url}: {e}")
                if self.exchange:
                    await self.exchange.close()

        # Clean up session if all attempts failed
        if self.session:
            try:
                await self.session.close()
                logger.info(f"Cleaned up session for failed {self.name} connection")
            except Exception as e:
                logger.warning(f"Error cleaning up session for {self.name}: {e}")
            finally:
                self.session = None

        self.last_error = "All connection attempts failed"
        self.is_connected = False
        await db_manager.update_exchange_status(self.name, "ERROR", self.last_error)
        logger.error(f"Failed to connect to {self.name} after trying all configurations")
        raise Exception(f"Failed to initialize {self.name}")

    def _get_ccxt_exchange_name(self, exchange_name: str) -> str:
        """Map exchange names to CCXT exchange names."""
        name_mapping = {
            'gate': 'gateio',  # Gate.io is 'gateio' in CCXT
            'mexc': 'mexc',
            'lbank': 'lbank'
        }
        return name_mapping.get(exchange_name.lower(), exchange_name.lower())
    
    async def _test_connection(self):
        """Test exchange connection with anti-detection measures."""
        try:
            # Add random delay before connection test
            await AntiDetectionManager.add_random_delay(1.0, 3.0)

            async with self.throttler:
                # Additional throttling based on last request time
                current_time = time.time()
                time_since_last = current_time - self.last_request_time
                if time_since_last < 2.0:  # Minimum 2 seconds between requests
                    await asyncio.sleep(2.0 - time_since_last)

                markets = await self.exchange.load_markets(True)
                self.last_request_time = time.time()
                self.request_count += 1

                if not markets:
                    raise Exception("No markets returned from exchange")
                logger.debug(f"Successfully loaded {len(markets)} markets from {self.name}")

        except Exception as e:
            logger.error(f"Connection test failed for {self.name}: {e}")
            logger.debug(f"Exchange object type: {type(self.exchange)}")
            logger.debug(f"Exchange config: {self.exchange.urls if hasattr(self.exchange, 'urls') else 'No URLs'}")
            raise
    
    async def fetch_ticker(self, symbol: str) -> Optional[PriceData]:
        """Fetch ticker data for a symbol with anti-detection measures."""
        if not self.is_connected:
            return None

        try:
            # Add random delay to mimic human behavior
            await AntiDetectionManager.add_random_delay(0.3, 1.5)

            async with self.throttler:
                # Additional throttling based on last request time
                current_time = time.time()
                time_since_last = current_time - self.last_request_time
                if time_since_last < 1.5:  # Minimum 1.5 seconds between requests
                    await asyncio.sleep(1.5 - time_since_last)

                ticker = await self.exchange.fetch_ticker(symbol)
                self.last_request_time = time.time()
                self.request_count += 1

                # Log request count periodically for monitoring
                if self.request_count % 50 == 0:
                    logger.info(f"{self.name}: Made {self.request_count} requests so far")

                return PriceData(
                    exchange=self.name,
                    symbol=symbol,
                    price=ticker['last'],
                    volume=ticker['baseVolume'],
                    bid=ticker['bid'],
                    ask=ticker['ask'],
                    timestamp=datetime.now()
                )

        except Exception as e:
            # Check if it's a rate limiting error
            error_str = str(e).lower()
            if any(keyword in error_str for keyword in ['rate limit', 'too many requests', '429', 'banned', 'blocked']):
                logger.warning(f"Rate limiting detected for {self.name}: {e}")
                # Exponential backoff for rate limiting
                backoff_time = random.uniform(30, 120)  # 30-120 seconds backoff
                logger.info(f"Backing off for {backoff_time:.1f} seconds due to rate limiting")
                await asyncio.sleep(backoff_time)
            else:
                logger.error(f"Error fetching ticker for {symbol} from {self.name}: {e}")

            self.last_error = str(e)
            await db_manager.update_exchange_status(self.name, "ERROR", str(e))
            return None
    
    async def fetch_order_book(self, symbol: str, limit: int = 5) -> Optional[Dict]:
        """Fetch order book data."""
        if not self.is_connected:
            return None
            
        try:
            async with self.throttler:
                order_book = await self.exchange.fetch_order_book(symbol, limit)
                return order_book
                
        except Exception as e:
            logger.error(f"Error fetching order book for {symbol} from {self.name}: {e}")
            return None
    
    async def close(self):
        """Close exchange connection."""
        if self.exchange:
            try:
                await self.exchange.close()
            except Exception as e:
                logger.warning(f"Error closing exchange {self.name}: {e}")

        if self.session:
            try:
                await self.session.close()
            except Exception as e:
                logger.warning(f"Error closing session for {self.name}: {e}")
            finally:
                self.session = None

        self.is_connected = False
        logger.info(f"Closed connection to {self.name}")


class ExchangeManager:
    """Manager for multiple exchange connections."""
    
    def __init__(self):
        self.connectors: Dict[str, ExchangeConnector] = {}
        self.enabled_exchanges = []
        
    async def initialize(self):
        """Initialize all enabled exchanges."""
        exchanges_config = config.exchanges
        
        for exchange_name, exchange_config in exchanges_config.items():
            if exchange_config.enabled:
                try:
                    if exchange_config.type == 'mock':
                        connector = MockExchangeConnector(exchange_name, exchange_config)
                    else:
                        connector = ExchangeConnector(exchange_name, exchange_config)
                        
                    await connector.initialize()
                    self.connectors[exchange_name] = connector
                    self.enabled_exchanges.append(exchange_name)
                    
                except Exception as e:
                    logger.error(f"Failed to initialize {exchange_name}: {e}")
                    # Continue with other exchanges
                    continue
        
        if not self.connectors:
            raise Exception("No exchanges could be initialized")
            
        logger.info(f"Initialized {len(self.connectors)} exchanges: {list(self.connectors.keys())}")
    
    async def fetch_all_prices(self, symbol: str) -> List[PriceData]:
        """Fetch prices from all connected exchanges for a symbol."""
        tasks = []
        
        for connector in self.connectors.values():
            if connector.is_connected:
                task = connector.fetch_ticker(symbol)
                tasks.append(task)
        
        if not tasks:
            logger.warning(f"No connected exchanges available for {symbol}")
            return []
        
        # Execute all requests concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter out None results and exceptions
        prices = []
        for result in results:
            if isinstance(result, PriceData):
                prices.append(result)
            elif isinstance(result, Exception):
                logger.error(f"Error in concurrent price fetch: {result}")
        
        return prices
    
    async def fetch_prices_batch(self, symbols: List[str]) -> Dict[str, List[PriceData]]:
        """Fetch prices for multiple symbols from all exchanges."""
        results = {}
        
        # Create tasks for all symbol-exchange combinations
        tasks = []
        task_info = []
        
        for symbol in symbols:
            for connector in self.connectors.values():
                if connector.is_connected:
                    task = connector.fetch_ticker(symbol)
                    tasks.append(task)
                    task_info.append((symbol, connector.name))
        
        if not tasks:
            logger.warning("No connected exchanges available for batch fetch")
            return results
        
        # Execute all requests concurrently
        task_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Group results by symbol
        for i, result in enumerate(task_results):
            symbol, exchange = task_info[i]
            
            if symbol not in results:
                results[symbol] = []
            
            if isinstance(result, PriceData):
                results[symbol].append(result)
            elif isinstance(result, Exception):
                logger.error(f"Error fetching {symbol} from {exchange}: {result}")
        
        return results
    
    def get_connected_exchanges(self) -> List[str]:
        """Get list of currently connected exchanges."""
        return [
            name for name, connector in self.connectors.items() 
            if connector.is_connected
        ]
    
    def get_exchange_status(self) -> Dict[str, Dict]:
        """Get status of all exchanges."""
        status = {}
        for name, connector in self.connectors.items():
            status[name] = {
                'connected': connector.is_connected,
                'last_error': connector.last_error,
                'enabled': name in self.enabled_exchanges
            }
        return status
    
    async def reconnect_failed_exchanges(self):
        """Attempt to reconnect failed exchanges with smart backoff."""
        for name, connector in self.connectors.items():
            if not connector.is_connected:
                try:
                    # Add longer delay before reconnection attempts
                    logger.info(f"Attempting to reconnect to {name} after delay...")
                    await AntiDetectionManager.add_random_delay(10.0, 30.0)  # 10-30 second delay

                    await connector.initialize()
                    logger.info(f"Successfully reconnected to {name}")
                except Exception as e:
                    error_str = str(e).lower()
                    if any(keyword in error_str for keyword in ['rate limit', 'banned', 'blocked', '429']):
                        logger.warning(f"Rate limiting still active for {name}, will retry later: {e}")
                    else:
                        logger.warning(f"Failed to reconnect to {name}: {e}")
                    continue
    
    async def health_check(self) -> Dict[str, bool]:
        """Perform health check on all exchanges."""
        health_status = {}
        
        for name, connector in self.connectors.items():
            try:
                if connector.is_connected:
                    # Try to fetch a simple ticker to test connectivity
                    test_symbol = "BTC/USDT"  # Most exchanges should have this
                    result = await connector.fetch_ticker(test_symbol)
                    health_status[name] = result is not None
                else:
                    health_status[name] = False
                    
            except Exception as e:
                logger.error(f"Health check failed for {name}: {e}")
                health_status[name] = False
                await db_manager.update_exchange_status(name, "ERROR", str(e))
        
        return health_status
    
    async def close_all(self):
        """Close all exchange connections."""
        tasks = []
        for connector in self.connectors.values():
            tasks.append(connector.close())
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
        
        self.connectors.clear()
        self.enabled_exchanges.clear()
        logger.info("Closed all exchange connections")


# Global exchange manager instance
exchange_manager = ExchangeManager()
