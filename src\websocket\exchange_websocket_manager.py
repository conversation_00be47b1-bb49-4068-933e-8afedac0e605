"""
Unified WebSocket exchange manager that coordinates all exchange WebSocket connections.
"""
import asyncio
from typing import Dict, List, Optional, Callable
from datetime import datetime

from src.config.settings import config
from src.websocket.manager import WebSocketManager
from src.websocket.exchanges import (
    create_gate_websocket,
    create_mexc_websocket,
    create_lbank_websocket
)
from src.websocket.data_normalizer import NormalizedTickerData
from src.database.models import db_manager
from src.utils.logger import logger


class ExchangeWebSocketManager(WebSocketManager):
    """Unified WebSocket manager for all supported exchanges."""
    
    def __init__(self):
        super().__init__()
        self.data_callbacks: List[Callable[[NormalizedTickerData], None]] = []
        self.arbitrage_callbacks: List[Callable[[List[NormalizedTickerData]], None]] = []
        
        # Performance tracking
        self.symbol_data_count: Dict[str, int] = {}
        self.last_arbitrage_check = datetime.now()
        
    async def initialize(self) -> bool:
        """Initialize WebSocket connections for all enabled exchanges."""
        logger.info("Initializing WebSocket exchange manager...")
        
        # Create WebSocket connections for enabled exchanges
        exchanges_config = config.exchanges
        
        for exchange_name, exchange_config in exchanges_config.items():
            if not exchange_config.enabled:
                logger.info(f"Skipping disabled exchange: {exchange_name}")
                continue
            
            try:
                websocket_manager = self._create_exchange_websocket(exchange_name, exchange_config)
                if websocket_manager:
                    # Set up data handler for this exchange
                    websocket_manager.set_data_handler(self.handle_ticker_data)
                    self.add_connection(exchange_name, websocket_manager)
                    logger.info(f"Added WebSocket connection for {exchange_name}")
                else:
                    logger.warning(f"Failed to create WebSocket for {exchange_name}")

            except Exception as e:
                logger.error(f"Error creating WebSocket for {exchange_name}: {e}")
                continue
        
        if not self.connections:
            logger.error("No WebSocket connections could be created")
            return False
        
        # Start all connections
        success = await self.start()
        if success:
            logger.info(f"WebSocket manager initialized with {len(self.get_connected_exchanges())} exchanges")
        
        return success
    
    def _create_exchange_websocket(self, exchange_name: str, exchange_config):
        """Create WebSocket manager for specific exchange."""
        creators = {
            "gate": create_gate_websocket,
            "mexc": create_mexc_websocket,
            "lbank": create_lbank_websocket
        }
        
        creator = creators.get(exchange_name.lower())
        if creator:
            return creator(exchange_config)
        
        logger.warning(f"No WebSocket implementation for exchange: {exchange_name}")
        return None
    
    async def subscribe_to_trading_pairs(self) -> bool:
        """Subscribe to all configured trading pairs."""
        trading_pairs = config.trading_pairs
        enabled_symbols = [pair.symbol for pair in trading_pairs if pair.enabled]
        
        if not enabled_symbols:
            logger.warning("No enabled trading pairs found")
            return False
        
        success_count = 0
        for symbol in enabled_symbols:
            try:
                success = await self.subscribe_to_symbol(symbol)
                if success:
                    success_count += 1
                    logger.info(f"Subscribed to {symbol}")
                else:
                    logger.error(f"Failed to subscribe to {symbol}")
                    
            except Exception as e:
                logger.error(f"Error subscribing to {symbol}: {e}")
        
        logger.info(f"Subscribed to {success_count}/{len(enabled_symbols)} trading pairs")
        return success_count > 0
    
    async def _subscribe_exchange_symbol(self, connection, exchange_name: str, symbol: str):
        """Subscribe to symbol on specific exchange."""
        try:
            # Call the exchange-specific subscription method
            if hasattr(connection, 'subscribe_ticker'):
                await connection.subscribe_ticker(symbol)
            else:
                logger.warning(f"Exchange {exchange_name} doesn't support ticker subscription")
                
        except Exception as e:
            logger.error(f"Failed to subscribe {exchange_name} to {symbol}: {e}")
            raise
    
    async def _unsubscribe_exchange_symbol(self, connection, exchange_name: str, symbol: str):
        """Unsubscribe from symbol on specific exchange."""
        try:
            # Call the exchange-specific unsubscription method
            if hasattr(connection, 'unsubscribe_ticker'):
                await connection.unsubscribe_ticker(symbol)
            else:
                logger.warning(f"Exchange {exchange_name} doesn't support ticker unsubscription")
                
        except Exception as e:
            logger.error(f"Failed to unsubscribe {exchange_name} from {symbol}: {e}")
            raise
    
    def add_data_callback(self, callback: Callable[[NormalizedTickerData], None]):
        """Add callback for individual ticker data updates."""
        self.data_callbacks.append(callback)
    
    def add_arbitrage_callback(self, callback: Callable[[List[NormalizedTickerData]], None]):
        """Add callback for arbitrage analysis with multiple exchange data."""
        self.arbitrage_callbacks.append(callback)
    
    async def handle_ticker_data(self, ticker_data: NormalizedTickerData):
        """Handle incoming ticker data and trigger arbitrage analysis."""
        try:
            # Update statistics
            self.symbol_data_count[ticker_data.symbol] = self.symbol_data_count.get(ticker_data.symbol, 0) + 1

            # Store data in cache
            self.latest_data[ticker_data.exchange][ticker_data.symbol] = ticker_data
            self.data_timestamps[ticker_data.exchange][ticker_data.symbol] = datetime.now()

            # Call individual data callbacks
            for callback in self.data_callbacks:
                try:
                    await callback(ticker_data)
                except Exception as e:
                    logger.error(f"Error in data callback: {e}")

            # Check for arbitrage opportunities
            await self._check_arbitrage_opportunities(ticker_data.symbol)

        except Exception as e:
            logger.error(f"Error handling ticker data: {e}")
    
    async def _check_arbitrage_opportunities(self, symbol: str):
        """Check for arbitrage opportunities for a specific symbol."""
        try:
            # Get latest data for this symbol from all exchanges
            symbol_data = self.get_latest_data(symbol)
            
            if len(symbol_data) < 2:
                return  # Need at least 2 exchanges for arbitrage
            
            # Convert to list for arbitrage analysis
            ticker_list = list(symbol_data.values())
            
            # Call arbitrage callbacks
            for callback in self.arbitrage_callbacks:
                try:
                    await callback(ticker_list)
                except Exception as e:
                    logger.error(f"Error in arbitrage callback: {e}")
            
            self.last_arbitrage_check = datetime.now()
            
        except Exception as e:
            logger.error(f"Error checking arbitrage opportunities: {e}")
    
    def get_performance_stats(self) -> Dict[str, any]:
        """Get detailed performance statistics."""
        base_stats = self.get_statistics()
        
        # Add exchange-specific performance data
        performance_stats = {
            **base_stats,
            "symbol_data_counts": dict(self.symbol_data_count),
            "last_arbitrage_check": self.last_arbitrage_check.isoformat(),
            "data_callbacks_count": len(self.data_callbacks),
            "arbitrage_callbacks_count": len(self.arbitrage_callbacks),
            "average_latency_ms": self._calculate_average_latency(),
            "data_freshness": self._calculate_data_freshness()
        }
        
        return performance_stats
    
    def _calculate_average_latency(self) -> float:
        """Calculate average data latency across all exchanges."""
        try:
            total_latency = 0
            count = 0
            current_time = datetime.now()
            
            for exchange_timestamps in self.data_timestamps.values():
                for timestamp in exchange_timestamps.values():
                    latency = (current_time - timestamp).total_seconds() * 1000  # ms
                    total_latency += latency
                    count += 1
            
            return total_latency / count if count > 0 else 0
            
        except Exception as e:
            logger.error(f"Error calculating average latency: {e}")
            return 0
    
    def _calculate_data_freshness(self) -> Dict[str, float]:
        """Calculate data freshness for each exchange."""
        try:
            freshness = {}
            current_time = datetime.now()
            
            for exchange, timestamps in self.data_timestamps.items():
                if timestamps:
                    latest_timestamp = max(timestamps.values())
                    age_seconds = (current_time - latest_timestamp).total_seconds()
                    freshness[exchange] = age_seconds
                else:
                    freshness[exchange] = float('inf')
            
            return freshness
            
        except Exception as e:
            logger.error(f"Error calculating data freshness: {e}")
            return {}


# Global instance
exchange_websocket_manager = ExchangeWebSocketManager()
