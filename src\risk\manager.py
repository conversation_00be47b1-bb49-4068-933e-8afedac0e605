"""
Comprehensive risk management system for arbitrage trading.
"""
from dataclasses import dataclass
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from enum import Enum
import asyncio

from src.config.settings import config
from src.database.models import ArbitrageSignal, db_manager
from src.websocket.data_normalizer import NormalizedTickerData
from src.profit.calculator import ProfitCalculation
from src.utils.logger import logger


class RiskLevel(Enum):
    """Risk levels for trading decisions."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class RiskDecision(Enum):
    """Risk management decisions."""
    APPROVE = "approve"
    REJECT = "reject"
    REDUCE_SIZE = "reduce_size"
    DELAY = "delay"


@dataclass
class RiskLimits:
    """Risk limits configuration."""
    # Profit thresholds
    min_profit_percent: float = 0.02  # 2% minimum profit
    max_profit_percent: float = 50.0  # 50% maximum (likely data error)
    
    # Exposure limits
    max_position_usd: float = 1000.0  # Maximum position size
    max_daily_volume_usd: float = 10000.0  # Maximum daily trading volume
    max_exchange_exposure_percent: float = 40.0  # Max 40% exposure per exchange
    
    # Slippage limits
    max_slippage_percent: float = 0.5  # 0.5% maximum slippage
    max_total_fees_percent: float = 1.0  # 1% maximum total fees
    
    # Volatility limits
    max_price_change_percent: float = 10.0  # 10% max price change in 1 hour
    min_volume_usd: float = 1000.0  # Minimum 24h volume
    
    # Timing limits
    max_signal_age_seconds: float = 30.0  # Maximum signal age
    min_cooldown_seconds: float = 60.0  # Minimum time between trades on same pair
    max_execution_time_seconds: float = 10.0  # Maximum execution time
    
    # Risk scores
    max_risk_score: float = 0.7  # Maximum acceptable risk score
    min_confidence: float = 0.6  # Minimum confidence level


@dataclass
class RiskAssessment:
    """Risk assessment result."""
    decision: RiskDecision
    risk_level: RiskLevel
    risk_score: float
    confidence: float
    reasons: List[str]
    recommended_size: Optional[float] = None
    delay_seconds: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'decision': self.decision.value,
            'risk_level': self.risk_level.value,
            'risk_score': self.risk_score,
            'confidence': self.confidence,
            'reasons': self.reasons,
            'recommended_size': self.recommended_size,
            'delay_seconds': self.delay_seconds
        }


class CircuitBreaker:
    """Circuit breaker for exchange-specific issues."""
    
    def __init__(self, exchange: str, failure_threshold: int = 5, 
                 recovery_time: int = 300):
        self.exchange = exchange
        self.failure_threshold = failure_threshold
        self.recovery_time = recovery_time
        self.failure_count = 0
        self.last_failure_time: Optional[datetime] = None
        self.is_open = False
    
    def record_failure(self):
        """Record a failure."""
        self.failure_count += 1
        self.last_failure_time = datetime.now()
        
        if self.failure_count >= self.failure_threshold:
            self.is_open = True
            logger.warning(f"Circuit breaker OPEN for {self.exchange}")
    
    def record_success(self):
        """Record a success."""
        if self.failure_count > 0:
            self.failure_count = max(0, self.failure_count - 1)
    
    def can_trade(self) -> bool:
        """Check if trading is allowed."""
        if not self.is_open:
            return True
        
        if self.last_failure_time:
            time_since_failure = (datetime.now() - self.last_failure_time).total_seconds()
            if time_since_failure >= self.recovery_time:
                self.is_open = False
                self.failure_count = 0
                logger.info(f"Circuit breaker CLOSED for {self.exchange}")
                return True
        
        return False
    
    def get_status(self) -> Dict[str, Any]:
        """Get circuit breaker status."""
        return {
            'exchange': self.exchange,
            'is_open': self.is_open,
            'failure_count': self.failure_count,
            'last_failure_time': self.last_failure_time.isoformat() if self.last_failure_time else None,
            'recovery_time_remaining': max(0, self.recovery_time - (
                (datetime.now() - self.last_failure_time).total_seconds()
                if self.last_failure_time else 0
            ))
        }


class RiskManager:
    """Comprehensive risk management system."""

    def __init__(self, limits: Optional[RiskLimits] = None):
        self.limits = limits or self._load_limits_from_config()
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.exposure_tracker: Dict[str, float] = {}  # Exchange -> current exposure
        self.daily_volume: float = 0.0
        self.last_trades: Dict[str, datetime] = {}  # Symbol -> last trade time
        self.price_history: Dict[str, List[Tuple[datetime, float]]] = {}

        # Initialize circuit breakers for exchanges
        self._initialize_circuit_breakers()

    def _load_limits_from_config(self) -> RiskLimits:
        """Load risk limits from configuration."""
        try:
            risk_config = config.risk_management

            return RiskLimits(
                min_profit_percent=risk_config.min_profit_percent,
                max_profit_percent=risk_config.max_profit_percent,
                max_position_usd=risk_config.max_position_usd,
                max_daily_volume_usd=risk_config.max_daily_volume_usd,
                max_exchange_exposure_percent=risk_config.max_exchange_exposure_percent,
                max_slippage_percent=risk_config.max_slippage_percent,
                max_total_fees_percent=risk_config.max_total_fees_percent,
                max_price_change_percent=risk_config.max_price_change_percent,
                min_volume_usd=risk_config.min_volume_usd,
                max_signal_age_seconds=risk_config.max_signal_age_seconds,
                min_cooldown_seconds=risk_config.min_cooldown_seconds,
                max_execution_time_seconds=risk_config.max_execution_time_seconds,
                max_risk_score=risk_config.max_risk_score,
                min_confidence=risk_config.min_confidence
            )
        except Exception as e:
            logger.warning(f"Error loading risk limits from config: {e}, using defaults")
            return RiskLimits()
    
    def _initialize_circuit_breakers(self):
        """Initialize circuit breakers for all exchanges."""
        exchanges_config = config.exchanges
        risk_config = config.risk_management

        for exchange_name in exchanges_config.keys():
            self.circuit_breakers[exchange_name] = CircuitBreaker(
                exchange=exchange_name,
                failure_threshold=risk_config.circuit_breaker_failure_threshold,
                recovery_time=risk_config.circuit_breaker_recovery_time
            )
    
    async def assess_risk(
        self,
        profit_calc: ProfitCalculation,
        ticker_data: List[NormalizedTickerData],
        trade_amount_usd: float = 100.0
    ) -> RiskAssessment:
        """
        Comprehensive risk assessment for an arbitrage opportunity.
        
        Args:
            profit_calc: Profit calculation result
            ticker_data: List of ticker data for the symbol
            trade_amount_usd: Proposed trade amount
            
        Returns:
            RiskAssessment with decision and reasoning
        """
        reasons = []
        risk_factors = []
        
        try:
            # 1. Profit validation
            profit_risk = self._assess_profit_risk(profit_calc, reasons)
            risk_factors.append(profit_risk)
            
            # 2. Exposure limits
            exposure_risk = self._assess_exposure_risk(
                profit_calc.buy_exchange, profit_calc.sell_exchange, 
                trade_amount_usd, reasons
            )
            risk_factors.append(exposure_risk)
            
            # 3. Slippage and fees
            cost_risk = self._assess_cost_risk(profit_calc, reasons)
            risk_factors.append(cost_risk)
            
            # 4. Volatility check
            volatility_risk = await self._assess_volatility_risk(
                profit_calc.symbol, ticker_data, reasons
            )
            risk_factors.append(volatility_risk)
            
            # 5. Timing validation
            timing_risk = await self._assess_timing_risk(profit_calc, reasons)
            risk_factors.append(timing_risk)
            
            # 6. Circuit breaker check
            circuit_risk = self._assess_circuit_breaker_risk(
                profit_calc.buy_exchange, profit_calc.sell_exchange, reasons
            )
            risk_factors.append(circuit_risk)
            
            # 7. Data quality check
            data_risk = self._assess_data_quality_risk(ticker_data, reasons)
            risk_factors.append(data_risk)
            
            # Calculate overall risk
            overall_risk = max(risk_factors)
            risk_level = self._determine_risk_level(overall_risk)
            
            # Make decision
            decision = self._make_risk_decision(
                overall_risk, profit_calc, trade_amount_usd, reasons
            )
            
            return RiskAssessment(
                decision=decision,
                risk_level=risk_level,
                risk_score=overall_risk,
                confidence=profit_calc.confidence,
                reasons=reasons,
                recommended_size=self._calculate_recommended_size(
                    trade_amount_usd, overall_risk
                ),
                delay_seconds=self._calculate_delay(overall_risk)
            )
            
        except Exception as e:
            logger.error(f"Error in risk assessment: {e}")
            return RiskAssessment(
                decision=RiskDecision.REJECT,
                risk_level=RiskLevel.CRITICAL,
                risk_score=1.0,
                confidence=0.0,
                reasons=[f"Risk assessment error: {str(e)}"]
            )
    
    def _assess_profit_risk(self, profit_calc: ProfitCalculation, 
                           reasons: List[str]) -> float:
        """Assess profit-related risks."""
        risk = 0.0
        
        # Check minimum profit
        if profit_calc.profit_percent < self.limits.min_profit_percent:
            reasons.append(f"Profit {profit_calc.profit_percent:.2f}% below minimum {self.limits.min_profit_percent:.2f}%")
            risk = max(risk, 0.8)
        
        # Check maximum profit (likely data error)
        if profit_calc.profit_percent > self.limits.max_profit_percent:
            reasons.append(f"Profit {profit_calc.profit_percent:.2f}% suspiciously high")
            risk = max(risk, 0.9)
        
        # Check if actually profitable
        if not profit_calc.is_profitable:
            reasons.append("Trade is not profitable after fees")
            risk = max(risk, 1.0)
        
        return risk
    
    def _assess_exposure_risk(self, buy_exchange: str, sell_exchange: str,
                             trade_amount: float, reasons: List[str]) -> float:
        """Assess exposure-related risks."""
        risk = 0.0
        
        # Check position size
        if trade_amount > self.limits.max_position_usd:
            reasons.append(f"Trade amount ${trade_amount:.2f} exceeds limit ${self.limits.max_position_usd:.2f}")
            risk = max(risk, 0.7)
        
        # Check daily volume
        if self.daily_volume + trade_amount > self.limits.max_daily_volume_usd:
            reasons.append(f"Daily volume limit would be exceeded")
            risk = max(risk, 0.6)
        
        # Check exchange exposure
        buy_exposure = self.exposure_tracker.get(buy_exchange, 0)
        sell_exposure = self.exposure_tracker.get(sell_exchange, 0)
        
        max_exposure = self.limits.max_daily_volume_usd * (self.limits.max_exchange_exposure_percent / 100)
        
        if buy_exposure + trade_amount > max_exposure:
            reasons.append(f"Buy exchange {buy_exchange} exposure limit exceeded")
            risk = max(risk, 0.5)
        
        if sell_exposure + trade_amount > max_exposure:
            reasons.append(f"Sell exchange {sell_exchange} exposure limit exceeded")
            risk = max(risk, 0.5)
        
        return risk
    
    def _assess_cost_risk(self, profit_calc: ProfitCalculation, 
                         reasons: List[str]) -> float:
        """Assess cost-related risks."""
        risk = 0.0
        
        # Check slippage
        total_slippage_percent = (profit_calc.total_slippage / 
                                (profit_calc.trade_amount * profit_calc.buy_price)) * 100
        
        if total_slippage_percent > self.limits.max_slippage_percent:
            reasons.append(f"Slippage {total_slippage_percent:.3f}% exceeds limit {self.limits.max_slippage_percent:.3f}%")
            risk = max(risk, 0.6)
        
        # Check total fees
        total_fees_percent = (profit_calc.total_fees / 
                            (profit_calc.trade_amount * profit_calc.buy_price)) * 100
        
        if total_fees_percent > self.limits.max_total_fees_percent:
            reasons.append(f"Total fees {total_fees_percent:.3f}% exceed limit {self.limits.max_total_fees_percent:.3f}%")
            risk = max(risk, 0.5)
        
        return risk

    async def _assess_volatility_risk(self, symbol: str,
                                     ticker_data: List[NormalizedTickerData],
                                     reasons: List[str]) -> float:
        """Assess volatility-related risks."""
        risk = 0.0

        try:
            # Check minimum volume
            min_volume = min(ticker.volume * ticker.last for ticker in ticker_data)
            if min_volume < self.limits.min_volume_usd:
                reasons.append(f"Volume ${min_volume:.2f} below minimum ${self.limits.min_volume_usd:.2f}")
                risk = max(risk, 0.4)

            # Check recent price volatility
            await self._update_price_history(symbol, ticker_data)
            volatility_risk = self._calculate_volatility_risk(symbol)

            if volatility_risk > 0.3:
                reasons.append(f"High price volatility detected")
                risk = max(risk, volatility_risk)

        except Exception as e:
            logger.error(f"Error assessing volatility risk: {e}")
            risk = max(risk, 0.3)

        return risk

    async def _assess_timing_risk(self, profit_calc: ProfitCalculation,
                                 reasons: List[str]) -> float:
        """Assess timing-related risks."""
        risk = 0.0

        # Check signal age
        signal_age = (datetime.now() - profit_calc.calculation_time).total_seconds()
        if signal_age > self.limits.max_signal_age_seconds:
            reasons.append(f"Signal age {signal_age:.1f}s exceeds limit {self.limits.max_signal_age_seconds:.1f}s")
            risk = max(risk, 0.7)

        # Check cooldown period
        last_trade_time = self.last_trades.get(profit_calc.symbol)
        if last_trade_time:
            time_since_last = (datetime.now() - last_trade_time).total_seconds()
            if time_since_last < self.limits.min_cooldown_seconds:
                reasons.append(f"Cooldown period not met for {profit_calc.symbol}")
                risk = max(risk, 0.5)

        return risk

    def _assess_circuit_breaker_risk(self, buy_exchange: str, sell_exchange: str,
                                   reasons: List[str]) -> float:
        """Assess circuit breaker risks."""
        risk = 0.0

        # Check buy exchange circuit breaker
        buy_cb = self.circuit_breakers.get(buy_exchange)
        if buy_cb and not buy_cb.can_trade():
            reasons.append(f"Circuit breaker open for {buy_exchange}")
            risk = max(risk, 1.0)

        # Check sell exchange circuit breaker
        sell_cb = self.circuit_breakers.get(sell_exchange)
        if sell_cb and not sell_cb.can_trade():
            reasons.append(f"Circuit breaker open for {sell_exchange}")
            risk = max(risk, 1.0)

        return risk

    def _assess_data_quality_risk(self, ticker_data: List[NormalizedTickerData],
                                 reasons: List[str]) -> float:
        """Assess data quality risks."""
        risk = 0.0

        # Check data freshness
        now = datetime.now()
        for ticker in ticker_data:
            age = (now - ticker.timestamp).total_seconds()
            if age > 60:  # 1 minute
                reasons.append(f"Stale data from {ticker.exchange}: {age:.1f}s old")
                risk = max(risk, 0.4)

        # Check spread reasonableness
        for ticker in ticker_data:
            if ticker.spread_percent > 5.0:  # 5% spread seems excessive
                reasons.append(f"Excessive spread on {ticker.exchange}: {ticker.spread_percent:.2f}%")
                risk = max(risk, 0.3)

        return risk

    def _determine_risk_level(self, risk_score: float) -> RiskLevel:
        """Determine risk level from score."""
        if risk_score >= 0.8:
            return RiskLevel.CRITICAL
        elif risk_score >= 0.6:
            return RiskLevel.HIGH
        elif risk_score >= 0.3:
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW

    def _make_risk_decision(self, risk_score: float, profit_calc: ProfitCalculation,
                           trade_amount: float, reasons: List[str]) -> RiskDecision:
        """Make final risk decision."""
        # Critical risk - always reject
        if risk_score >= 0.8:
            return RiskDecision.REJECT

        # High risk - reject if low profit
        if risk_score >= 0.6:
            if profit_calc.profit_percent < self.limits.min_profit_percent * 2:
                return RiskDecision.REJECT
            else:
                return RiskDecision.REDUCE_SIZE

        # Medium risk - reduce size or delay
        if risk_score >= 0.3:
            if profit_calc.profit_percent >= self.limits.min_profit_percent * 1.5:
                return RiskDecision.REDUCE_SIZE
            else:
                return RiskDecision.DELAY

        # Low risk - approve
        return RiskDecision.APPROVE

    def _calculate_recommended_size(self, original_size: float,
                                   risk_score: float) -> Optional[float]:
        """Calculate recommended trade size based on risk."""
        if risk_score >= 0.6:
            return original_size * 0.5  # Reduce by 50%
        elif risk_score >= 0.3:
            return original_size * 0.75  # Reduce by 25%
        else:
            return original_size

    def _calculate_delay(self, risk_score: float) -> Optional[float]:
        """Calculate recommended delay based on risk."""
        if risk_score >= 0.4:
            return 30.0  # 30 second delay
        elif risk_score >= 0.2:
            return 10.0  # 10 second delay
        else:
            return None

    async def _update_price_history(self, symbol: str,
                                   ticker_data: List[NormalizedTickerData]):
        """Update price history for volatility calculation."""
        if symbol not in self.price_history:
            self.price_history[symbol] = []

        # Add current prices
        now = datetime.now()
        for ticker in ticker_data:
            self.price_history[symbol].append((now, ticker.last))

        # Keep only last hour of data
        cutoff_time = now - timedelta(hours=1)
        self.price_history[symbol] = [
            (timestamp, price) for timestamp, price in self.price_history[symbol]
            if timestamp > cutoff_time
        ]

    def _calculate_volatility_risk(self, symbol: str) -> float:
        """Calculate volatility risk from price history."""
        if symbol not in self.price_history or len(self.price_history[symbol]) < 2:
            return 0.0

        prices = [price for _, price in self.price_history[symbol]]

        if not prices:
            return 0.0

        # Calculate price change percentage
        min_price = min(prices)
        max_price = max(prices)

        if min_price > 0:
            price_change_percent = ((max_price - min_price) / min_price) * 100

            if price_change_percent > self.limits.max_price_change_percent:
                return min(price_change_percent / self.limits.max_price_change_percent, 1.0)

        return 0.0

    def record_trade_execution(self, symbol: str, buy_exchange: str,
                              sell_exchange: str, amount_usd: float,
                              success: bool):
        """Record trade execution for tracking."""
        now = datetime.now()

        # Update last trade time
        self.last_trades[symbol] = now

        # Update exposure tracking
        if success:
            self.exposure_tracker[buy_exchange] = self.exposure_tracker.get(buy_exchange, 0) + amount_usd
            self.exposure_tracker[sell_exchange] = self.exposure_tracker.get(sell_exchange, 0) + amount_usd
            self.daily_volume += amount_usd

            # Record success for circuit breakers
            if buy_exchange in self.circuit_breakers:
                self.circuit_breakers[buy_exchange].record_success()
            if sell_exchange in self.circuit_breakers:
                self.circuit_breakers[sell_exchange].record_success()
        else:
            # Record failure for circuit breakers
            if buy_exchange in self.circuit_breakers:
                self.circuit_breakers[buy_exchange].record_failure()
            if sell_exchange in self.circuit_breakers:
                self.circuit_breakers[sell_exchange].record_failure()

    def get_risk_statistics(self) -> Dict[str, Any]:
        """Get comprehensive risk statistics."""
        return {
            'limits': {
                'min_profit_percent': self.limits.min_profit_percent,
                'max_position_usd': self.limits.max_position_usd,
                'max_daily_volume_usd': self.limits.max_daily_volume_usd,
                'max_slippage_percent': self.limits.max_slippage_percent,
                'max_risk_score': self.limits.max_risk_score
            },
            'current_exposure': dict(self.exposure_tracker),
            'daily_volume': self.daily_volume,
            'circuit_breakers': {
                exchange: cb.get_status()
                for exchange, cb in self.circuit_breakers.items()
            },
            'recent_trades': len(self.last_trades),
            'price_history_symbols': list(self.price_history.keys())
        }

    def reset_daily_limits(self):
        """Reset daily limits (call at start of each day)."""
        self.daily_volume = 0.0
        self.exposure_tracker.clear()
        logger.info("Daily risk limits reset")


# Global instance
risk_manager = RiskManager()
