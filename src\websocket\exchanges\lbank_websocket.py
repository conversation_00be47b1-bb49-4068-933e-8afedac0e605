"""
LBank WebSocket implementation for real-time ticker data.
"""
import json
from typing import Dict, Any, List
from datetime import datetime

from src.websocket.base import BaseWebSocketManager, WebSocketConfig
from src.websocket.data_normalizer import <PERSON>Normalizer, NormalizedTickerData
from src.utils.logger import logger


class LBankWebSocketManager(BaseWebSocketManager):
    """LBank WebSocket manager for ticker data."""
    
    def __init__(self, config: WebSocketConfig):
        super().__init__("lbank", config)
        self.subscribed_symbols: List[str] = []
        self.ping_counter = 0
    
    async def _on_connected(self):
        """Called when WebSocket connection is established."""
        logger.info(f"{self.exchange_name}: WebSocket connected, ready for subscriptions")
    
    async def _on_message(self, data: Dict[str, Any]):
        """Handle incoming WebSocket message."""
        try:
            # LBank sends different message types
            if "type" in data:
                message_type = data["type"]
                
                if message_type == "ticker":
                    await self._handle_ticker_data(data)
                elif message_type == "pong":
                    await self._handle_pong(data)
                elif message_type == "subscribe":
                    await self._handle_subscription_response(data)
                else:
                    logger.debug(f"{self.exchange_name}: Unhandled message type: {message_type}")
            
            elif "action" in data:
                # Response to subscription request
                await self._handle_action_response(data)
            
            elif "error" in data:
                logger.error(f"{self.exchange_name}: WebSocket error: {data['error']}")
            
        except Exception as e:
            logger.error(f"{self.exchange_name}: Error processing message: {e}")
    
    async def _send_ping(self):
        """Send ping message to maintain connection."""
        self.ping_counter += 1
        ping_message = {
            "action": "ping",
            "ping": str(self.ping_counter)
        }
        await self.send_message(ping_message)
    
    def _is_pong_message(self, data: Dict[str, Any]) -> bool:
        """Check if message is a pong response."""
        return data.get("type") == "pong" or data.get("action") == "pong"
    
    def _get_message_type(self, data: Dict[str, Any]) -> str:
        """Extract message type from received data."""
        if "type" in data:
            return data["type"]
        elif "action" in data:
            return data["action"]
        return "unknown"
    
    async def subscribe_ticker(self, symbol: str) -> bool:
        """Subscribe to ticker updates for a symbol."""
        try:
            # Convert symbol format (e.g., "ADA/USDT" -> "ada_usdt")
            lbank_symbol = symbol.replace("/", "_").lower()
            
            subscription_message = {
                "action": "subscribe",
                "subscribe": "ticker",
                "pair": lbank_symbol
            }
            
            success = await self.send_message(subscription_message)
            if success:
                self.subscribed_symbols.append(symbol)
                logger.info(f"{self.exchange_name}: Subscribed to ticker for {symbol}")
            
            return success
            
        except Exception as e:
            logger.error(f"{self.exchange_name}: Failed to subscribe to {symbol}: {e}")
            return False
    
    async def unsubscribe_ticker(self, symbol: str) -> bool:
        """Unsubscribe from ticker updates for a symbol."""
        try:
            lbank_symbol = symbol.replace("/", "_").lower()
            
            unsubscription_message = {
                "action": "unsubscribe",
                "subscribe": "ticker",
                "pair": lbank_symbol
            }
            
            success = await self.send_message(unsubscription_message)
            if success and symbol in self.subscribed_symbols:
                self.subscribed_symbols.remove(symbol)
                logger.info(f"{self.exchange_name}: Unsubscribed from ticker for {symbol}")
            
            return success
            
        except Exception as e:
            logger.error(f"{self.exchange_name}: Failed to unsubscribe from {symbol}: {e}")
            return False
    
    async def _handle_ticker_data(self, data: Dict[str, Any]):
        """Handle ticker data message."""
        try:
            if "data" in data and "pair" in data:
                lbank_symbol = data["pair"]
                
                # Convert back to standard format
                symbol = lbank_symbol.replace("_", "/").upper()
                
                # Normalize the data
                normalized_data = DataNormalizer.normalize_lbank_ticker(data, symbol)
                
                if normalized_data and DataNormalizer.validate_ticker_data(normalized_data):
                    # Store in database
                    price_data = DataNormalizer.to_price_data(normalized_data)
                    await self._store_price_data(price_data)
                    
                    # Notify handlers
                    await self._notify_data_handlers(normalized_data)
                    
                    logger.debug(f"{self.exchange_name}: Processed ticker for {symbol}")
                else:
                    logger.warning(f"{self.exchange_name}: Invalid ticker data for {symbol}")
        
        except Exception as e:
            logger.error(f"{self.exchange_name}: Error handling ticker data: {e}")
    
    async def _handle_pong(self, data: Dict[str, Any]):
        """Handle pong message."""
        try:
            pong_value = data.get("pong", "")
            logger.debug(f"{self.exchange_name}: Received pong: {pong_value}")
        
        except Exception as e:
            logger.error(f"{self.exchange_name}: Error handling pong: {e}")
    
    async def _handle_subscription_response(self, data: Dict[str, Any]):
        """Handle subscription response."""
        try:
            subscribe_type = data.get("subscribe", "")
            pair = data.get("pair", "")
            
            logger.debug(f"{self.exchange_name}: Subscription response for {subscribe_type} on {pair}")
            
        except Exception as e:
            logger.error(f"{self.exchange_name}: Error handling subscription response: {e}")
    
    async def _handle_action_response(self, data: Dict[str, Any]):
        """Handle action response."""
        try:
            action = data.get("action", "")
            
            if action == "pong":
                await self._handle_pong(data)
            else:
                logger.debug(f"{self.exchange_name}: Action response: {action}")
            
        except Exception as e:
            logger.error(f"{self.exchange_name}: Error handling action response: {e}")
    
    async def _store_price_data(self, price_data):
        """Store price data in database."""
        try:
            from src.database.models import db_manager
            await db_manager.insert_price(price_data)
        except Exception as e:
            logger.error(f"{self.exchange_name}: Failed to store price data: {e}")
    
    async def _notify_data_handlers(self, normalized_data: NormalizedTickerData):
        """Notify registered data handlers."""
        # Call the data handler if set by the manager
        if hasattr(self, '_data_handler') and self._data_handler:
            try:
                await self._data_handler(normalized_data)
            except Exception as e:
                logger.error(f"{self.exchange_name}: Error in data handler: {e}")

    def set_data_handler(self, handler):
        """Set the data handler callback."""
        self._data_handler = handler


def create_lbank_websocket(exchange_config) -> LBankWebSocketManager:
    """Create LBank WebSocket manager with configuration."""
    # LBank WebSocket URL
    ws_url = "wss://www.lbank.info/ws/V2/"
    
    config = WebSocketConfig(
        url=ws_url,
        ping_interval=30,
        ping_timeout=10,
        max_reconnect_attempts=10,
        initial_reconnect_delay=1.0,
        max_reconnect_delay=60.0,
        reconnect_backoff_factor=2.0,
        connection_timeout=exchange_config.timeout,
        message_timeout=5
    )
    
    return LBankWebSocketManager(config)
