#!/usr/bin/env python3
"""
Test script to verify the bot setup is working correctly.
"""
import sys
import os
from pathlib import Path

def test_imports():
    """Test if all imports work correctly."""
    print("🧪 Testing imports...")
    
    # Add src to path
    src_dir = Path(__file__).parent / "src"
    sys.path.insert(0, str(src_dir))
    os.chdir(src_dir)
    
    try:
        # Test basic imports
        from config.settings import config
        print("  ✅ Config settings imported successfully")
        
        from utils.logger import logger
        print("  ✅ Logger imported successfully")
        
        from websocket.data_normalizer import NormalizedTickerData
        print("  ✅ WebSocket data normalizer imported successfully")
        
        from profit.calculator import ProfitCalculator
        print("  ✅ Profit calculator imported successfully")
        
        from risk.manager import RiskManager
        print("  ✅ Risk manager imported successfully")
        
        from monitoring.performance_monitor import PerformanceMonitor
        print("  ✅ Performance monitor imported successfully")
        
        from arbitrage.realtime_engine import RealtimeArbitrageEngine
        print("  ✅ Realtime arbitrage engine imported successfully")
        
        from notifications.telegram_bot import telegram_notifier
        print("  ✅ Telegram notifier imported successfully")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config():
    """Test if configuration is loaded correctly."""
    print("\n⚙️ Testing configuration...")
    
    try:
        from config.settings import config
        
        # Test basic config access
        bot_config = config.bot
        print(f"  ✅ Bot config loaded: profit_threshold = {bot_config.profit_threshold}")
        
        trading_pairs = config.trading_pairs
        print(f"  ✅ Trading pairs loaded: {len(trading_pairs)} pairs")
        
        exchanges = config.exchanges
        print(f"  ✅ Exchanges config loaded: {len(exchanges)} exchanges")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Config test failed: {e}")
        return False

def test_environment():
    """Test environment setup."""
    print("\n🌍 Testing environment...")
    
    # Check .env file
    env_file = Path(".env")
    if env_file.exists():
        print("  ✅ .env file found")
    else:
        print("  ⚠️  .env file not found (will use defaults)")
    
    # Check directories
    for directory in ["data", "logs", "config"]:
        if Path(directory).exists():
            print(f"  ✅ {directory}/ directory exists")
        else:
            print(f"  ⚠️  {directory}/ directory missing")
    
    # Check config file
    config_file = Path("config/config.yaml")
    if config_file.exists():
        print("  ✅ config/config.yaml found")
    else:
        print("  ❌ config/config.yaml missing")
        return False
    
    return True

def test_dependencies():
    """Test if required dependencies are available."""
    print("\n📦 Testing dependencies...")
    
    required_modules = [
        'asyncio', 'aiohttp', 'websockets', 'pydantic', 
        'yaml', 'dotenv', 'psutil', 'pathlib'
    ]
    
    missing = []
    
    for module in required_modules:
        try:
            if module == 'yaml':
                import yaml
            elif module == 'dotenv':
                import dotenv
            else:
                __import__(module)
            print(f"  ✅ {module}")
        except ImportError:
            print(f"  ❌ {module} (missing)")
            missing.append(module)
    
    if missing:
        print(f"\n❌ Missing dependencies: {', '.join(missing)}")
        print("Run: pip install -r requirements.txt")
        return False
    
    return True

def main():
    """Run all tests."""
    print("🔍 Enhanced Arbitrage Bot Setup Test")
    print("=" * 40)
    
    tests = [
        ("Dependencies", test_dependencies),
        ("Environment", test_environment),
        ("Imports", test_imports),
        ("Configuration", test_config)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 40)
    print("📋 TEST SUMMARY")
    print("=" * 40)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 40)
    if all_passed:
        print("🎉 ALL TESTS PASSED! The bot is ready to run.")
        print("\nTo start the bot, run:")
        print("  python start_bot.py")
    else:
        print("❌ SOME TESTS FAILED! Please fix the issues above.")
        print("\nCommon fixes:")
        print("  - Install dependencies: pip install -r requirements.txt")
        print("  - Create .env file with Telegram credentials")
        print("  - Make sure config/config.yaml exists")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    exit_code = main()
    
    # Keep window open on Windows
    if os.name == 'nt':
        input("\nPress Enter to exit...")
    
    sys.exit(exit_code)
