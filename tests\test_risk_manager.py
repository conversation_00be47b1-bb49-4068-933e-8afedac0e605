#!/usr/bin/env python3
"""
Unit tests for risk management system.
"""
import pytest
import asyncio
import sys
from datetime import datetime, timedelta
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.risk.manager import (
    RiskManager, RiskLimits, RiskAssessment, RiskLevel, RiskDecision, CircuitBreaker
)
from src.profit.calculator import ProfitCalculation
from src.websocket.data_normalizer import NormalizedTickerData


class TestRiskLimits:
    """Test risk limits configuration."""
    
    def test_default_risk_limits(self):
        """Test default risk limits values."""
        limits = RiskLimits()
        
        assert limits.min_profit_percent == 0.02
        assert limits.max_profit_percent == 50.0
        assert limits.max_position_usd == 1000.0
        assert limits.max_daily_volume_usd == 10000.0
        assert limits.max_exchange_exposure_percent == 40.0
        assert limits.max_slippage_percent == 0.5
        assert limits.max_total_fees_percent == 1.0
        assert limits.max_price_change_percent == 10.0
        assert limits.min_volume_usd == 1000.0
        assert limits.max_signal_age_seconds == 30.0
        assert limits.min_cooldown_seconds == 60.0
        assert limits.max_execution_time_seconds == 10.0
        assert limits.max_risk_score == 0.7
        assert limits.min_confidence == 0.6
    
    def test_custom_risk_limits(self):
        """Test custom risk limits values."""
        limits = RiskLimits(
            min_profit_percent=0.05,
            max_position_usd=500.0,
            max_risk_score=0.5
        )
        
        assert limits.min_profit_percent == 0.05
        assert limits.max_position_usd == 500.0
        assert limits.max_risk_score == 0.5


class TestRiskAssessment:
    """Test risk assessment functionality."""
    
    def test_risk_assessment_creation(self):
        """Test risk assessment creation."""
        assessment = RiskAssessment(
            decision=RiskDecision.APPROVE,
            risk_level=RiskLevel.LOW,
            risk_score=0.2,
            confidence=0.8,
            reasons=["Low risk opportunity"],
            recommended_size=100.0
        )
        
        assert assessment.decision == RiskDecision.APPROVE
        assert assessment.risk_level == RiskLevel.LOW
        assert assessment.risk_score == 0.2
        assert assessment.confidence == 0.8
        assert len(assessment.reasons) == 1
        assert assessment.recommended_size == 100.0
    
    def test_risk_assessment_to_dict(self):
        """Test risk assessment dictionary conversion."""
        assessment = RiskAssessment(
            decision=RiskDecision.REJECT,
            risk_level=RiskLevel.HIGH,
            risk_score=0.8,
            confidence=0.3,
            reasons=["High risk", "Low confidence"]
        )
        
        result = assessment.to_dict()
        
        assert isinstance(result, dict)
        assert result['decision'] == 'reject'
        assert result['risk_level'] == 'high'
        assert result['risk_score'] == 0.8
        assert result['confidence'] == 0.3
        assert len(result['reasons']) == 2


class TestCircuitBreaker:
    """Test circuit breaker functionality."""
    
    def test_circuit_breaker_initialization(self):
        """Test circuit breaker initialization."""
        cb = CircuitBreaker("test_exchange", failure_threshold=3, recovery_time=60)
        
        assert cb.exchange == "test_exchange"
        assert cb.failure_threshold == 3
        assert cb.recovery_time == 60
        assert cb.failure_count == 0
        assert cb.is_open is False
        assert cb.can_trade() is True
    
    def test_circuit_breaker_failure_recording(self):
        """Test circuit breaker failure recording."""
        cb = CircuitBreaker("test_exchange", failure_threshold=2, recovery_time=60)
        
        # Record first failure
        cb.record_failure()
        assert cb.failure_count == 1
        assert cb.is_open is False
        assert cb.can_trade() is True
        
        # Record second failure - should open circuit
        cb.record_failure()
        assert cb.failure_count == 2
        assert cb.is_open is True
        assert cb.can_trade() is False
    
    def test_circuit_breaker_success_recording(self):
        """Test circuit breaker success recording."""
        cb = CircuitBreaker("test_exchange", failure_threshold=3, recovery_time=60)
        
        # Record failure then success
        cb.record_failure()
        assert cb.failure_count == 1
        
        cb.record_success()
        assert cb.failure_count == 0
    
    def test_circuit_breaker_recovery(self):
        """Test circuit breaker recovery after time."""
        cb = CircuitBreaker("test_exchange", failure_threshold=1, recovery_time=0)
        
        # Trigger circuit breaker
        cb.record_failure()
        assert cb.is_open is True
        assert cb.can_trade() is False
        
        # Should recover immediately due to recovery_time=0
        assert cb.can_trade() is True
        assert cb.is_open is False
        assert cb.failure_count == 0
    
    def test_circuit_breaker_status(self):
        """Test circuit breaker status reporting."""
        cb = CircuitBreaker("test_exchange", failure_threshold=2, recovery_time=60)
        
        status = cb.get_status()
        
        assert isinstance(status, dict)
        assert status['exchange'] == "test_exchange"
        assert status['is_open'] is False
        assert status['failure_count'] == 0
        assert status['last_failure_time'] is None
        assert status['recovery_time_remaining'] >= 0


class TestRiskManager:
    """Test risk manager functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.limits = RiskLimits(
            min_profit_percent=0.01,
            max_position_usd=500.0,
            max_daily_volume_usd=5000.0,
            max_risk_score=0.6
        )
        self.risk_manager = RiskManager(self.limits)
        
        # Create test data
        self.profit_calc = ProfitCalculation(
            symbol="ADA/USDT",
            buy_exchange="gate",
            sell_exchange="mexc",
            buy_price=0.5000,
            sell_price=0.5020,
            trade_amount=200.0,
            buy_trading_fee=0.10,
            sell_trading_fee=0.10,
            withdrawal_fee=1.0,
            network_fee=0.1,
            total_fees=1.3,
            buy_slippage=0.05,
            sell_slippage=0.05,
            total_slippage=0.10,
            gross_profit=4.0,
            net_profit=2.6,
            profit_percent=2.6,
            roi_percent=2.6,
            risk_score=0.3,
            confidence=0.8,
            calculation_time=datetime.now(),
            is_profitable=True
        )
        
        self.ticker_data = [
            NormalizedTickerData(
                symbol="ADA/USDT",
                bid=0.4995,
                ask=0.5000,
                last=0.4998,
                volume=1000000,
                timestamp=datetime.now(),
                exchange="gate"
            ),
            NormalizedTickerData(
                symbol="ADA/USDT",
                bid=0.5020,
                ask=0.5025,
                last=0.5022,
                volume=800000,
                timestamp=datetime.now(),
                exchange="mexc"
            )
        ]
    
    def test_risk_manager_initialization(self):
        """Test risk manager initialization."""
        rm = RiskManager()
        
        assert isinstance(rm.limits, RiskLimits)
        assert len(rm.circuit_breakers) > 0  # Should have circuit breakers for exchanges
        assert rm.daily_volume == 0.0
        assert len(rm.exposure_tracker) == 0
        assert len(rm.last_trades) == 0
    
    def test_risk_manager_with_custom_limits(self):
        """Test risk manager with custom limits."""
        custom_limits = RiskLimits(min_profit_percent=0.05)
        rm = RiskManager(custom_limits)
        
        assert rm.limits.min_profit_percent == 0.05
    
    @pytest.mark.asyncio
    async def test_assess_risk_approve(self):
        """Test risk assessment that should be approved."""
        assessment = await self.risk_manager.assess_risk(
            profit_calc=self.profit_calc,
            ticker_data=self.ticker_data,
            trade_amount_usd=100.0
        )
        
        assert isinstance(assessment, RiskAssessment)
        assert assessment.decision in [RiskDecision.APPROVE, RiskDecision.REDUCE_SIZE]
        assert assessment.risk_level in [RiskLevel.LOW, RiskLevel.MEDIUM]
        assert 0 <= assessment.risk_score <= 1
        assert 0 <= assessment.confidence <= 1
        assert isinstance(assessment.reasons, list)
    
    @pytest.mark.asyncio
    async def test_assess_risk_reject_low_profit(self):
        """Test risk assessment that should be rejected due to low profit."""
        # Create low profit calculation
        low_profit_calc = ProfitCalculation(
            symbol="ADA/USDT",
            buy_exchange="gate",
            sell_exchange="mexc",
            buy_price=0.5000,
            sell_price=0.5005,  # Very small profit
            trade_amount=200.0,
            buy_trading_fee=0.10,
            sell_trading_fee=0.10,
            withdrawal_fee=1.0,
            network_fee=0.1,
            total_fees=1.3,
            buy_slippage=0.05,
            sell_slippage=0.05,
            total_slippage=0.10,
            gross_profit=1.0,
            net_profit=-0.3,  # Negative profit
            profit_percent=0.005,  # 0.5% - below minimum
            roi_percent=0.005,
            risk_score=0.3,
            confidence=0.8,
            calculation_time=datetime.now(),
            is_profitable=False
        )
        
        assessment = await self.risk_manager.assess_risk(
            profit_calc=low_profit_calc,
            ticker_data=self.ticker_data,
            trade_amount_usd=100.0
        )
        
        assert assessment.decision == RiskDecision.REJECT
        assert any("profit" in reason.lower() for reason in assessment.reasons)
    
    @pytest.mark.asyncio
    async def test_assess_risk_reject_large_position(self):
        """Test risk assessment that should be rejected due to large position."""
        assessment = await self.risk_manager.assess_risk(
            profit_calc=self.profit_calc,
            ticker_data=self.ticker_data,
            trade_amount_usd=1000.0  # Exceeds max_position_usd (500)
        )
        
        assert assessment.decision in [RiskDecision.REJECT, RiskDecision.REDUCE_SIZE]
        assert any("amount" in reason.lower() or "limit" in reason.lower() 
                  for reason in assessment.reasons)
    
    @pytest.mark.asyncio
    async def test_assess_risk_circuit_breaker(self):
        """Test risk assessment with circuit breaker open."""
        # Open circuit breaker for buy exchange
        cb = self.risk_manager.circuit_breakers.get("gate")
        if cb:
            cb.is_open = True
        
        assessment = await self.risk_manager.assess_risk(
            profit_calc=self.profit_calc,
            ticker_data=self.ticker_data,
            trade_amount_usd=100.0
        )
        
        assert assessment.decision == RiskDecision.REJECT
        assert any("circuit breaker" in reason.lower() for reason in assessment.reasons)
    
    def test_record_trade_execution_success(self):
        """Test recording successful trade execution."""
        initial_volume = self.risk_manager.daily_volume
        initial_exposure = self.risk_manager.exposure_tracker.get("gate", 0)
        
        self.risk_manager.record_trade_execution(
            symbol="ADA/USDT",
            buy_exchange="gate",
            sell_exchange="mexc",
            amount_usd=100.0,
            success=True
        )
        
        assert self.risk_manager.daily_volume == initial_volume + 100.0
        assert self.risk_manager.exposure_tracker["gate"] == initial_exposure + 100.0
        assert "ADA/USDT" in self.risk_manager.last_trades
    
    def test_record_trade_execution_failure(self):
        """Test recording failed trade execution."""
        initial_volume = self.risk_manager.daily_volume
        
        self.risk_manager.record_trade_execution(
            symbol="ADA/USDT",
            buy_exchange="gate",
            sell_exchange="mexc",
            amount_usd=100.0,
            success=False
        )
        
        # Volume should not increase on failure
        assert self.risk_manager.daily_volume == initial_volume
        # But last trade time should still be recorded
        assert "ADA/USDT" in self.risk_manager.last_trades
    
    def test_get_risk_statistics(self):
        """Test risk statistics reporting."""
        stats = self.risk_manager.get_risk_statistics()
        
        assert isinstance(stats, dict)
        assert 'limits' in stats
        assert 'current_exposure' in stats
        assert 'daily_volume' in stats
        assert 'circuit_breakers' in stats
        assert 'recent_trades' in stats
        
        # Check limits structure
        limits = stats['limits']
        assert 'min_profit_percent' in limits
        assert 'max_position_usd' in limits
        assert 'max_daily_volume_usd' in limits
    
    def test_reset_daily_limits(self):
        """Test resetting daily limits."""
        # Set some values
        self.risk_manager.daily_volume = 1000.0
        self.risk_manager.exposure_tracker["gate"] = 500.0
        
        # Reset
        self.risk_manager.reset_daily_limits()
        
        assert self.risk_manager.daily_volume == 0.0
        assert len(self.risk_manager.exposure_tracker) == 0


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
