"""
Arbitrage detection engine for identifying profitable trading opportunities.
"""
import asyncio
from datetime import datetime, timed<PERSON><PERSON>
from typing import List, Dict, Optional, Tuple
from itertools import combinations

from src.config.settings import config
from src.database.models import PriceData, ArbitrageSignal, db_manager
from src.exchanges.exchange_manager import exchange_manager
from src.websocket.data_normalizer import NormalizedTickerData
from src.profit.calculator import profit_calculator, ProfitCalculation
from src.utils.logger import logger


class ArbitrageEngine:
    """Engine for detecting arbitrage opportunities."""
    
    def __init__(self):
        self.last_signals = {}  # Track last signal time per pair
        self.price_cache = {}   # Brief price cache to avoid duplicate calculations
        self.cache_duration = 5  # seconds
        
    async def analyze_symbol(self, symbol: str) -> List[ArbitrageSignal]:
        """Analyze a single symbol for arbitrage opportunities."""
        # Check cache first
        cache_key = f"{symbol}_{datetime.now().timestamp() // self.cache_duration}"
        if cache_key in self.price_cache:
            prices = self.price_cache[cache_key]
        else:
            # Fetch fresh prices
            prices = await exchange_manager.fetch_all_prices(symbol)
            if len(prices) < 2:
                logger.warning(f"Not enough price data for {symbol}: {len(prices)} exchanges")
                return []
            
            # Cache the prices briefly
            self.price_cache[cache_key] = prices
            # Clean old cache entries
            self._clean_price_cache()
        
        # Store prices in database
        if prices:
            await db_manager.insert_prices_batch(prices)
        
        # Find arbitrage opportunities
        opportunities = await self._find_opportunities(symbol, prices)
        
        # Filter and validate opportunities
        valid_opportunities = []
        for opportunity in opportunities:
            if await self._validate_opportunity(opportunity):
                valid_opportunities.append(opportunity)
        
        return valid_opportunities
    
    async def analyze_all_symbols(self) -> Dict[str, List[ArbitrageSignal]]:
        """Analyze all configured symbols for arbitrage opportunities."""
        results = {}
        trading_pairs = config.trading_pairs
        
        # Get enabled symbols
        enabled_symbols = [pair.symbol for pair in trading_pairs if pair.enabled]
        
        if not enabled_symbols:
            logger.warning("No enabled trading pairs found")
            return results
        
        # Analyze each symbol
        for symbol in enabled_symbols:
            try:
                opportunities = await self.analyze_symbol(symbol)
                if opportunities:
                    results[symbol] = opportunities
                    for opp in opportunities:
                        logger.info(f"Arbitrage opportunity for {opp.symbol}: "
                                    f"Buy on {opp.buy_exchange} at {opp.buy_price}, "
                                    f"Sell on {opp.sell_exchange} at {opp.sell_price}, "
                                    f"Profit: {opp.profit_percent}%")
                
            except Exception as e:
                logger.error(f"Error analyzing {symbol}: {e}")
                continue
        
        return results
    
    async def _find_opportunities(self, symbol: str, prices: List[PriceData]) -> List[ArbitrageSignal]:
        """Find arbitrage opportunities from price data."""
        opportunities = []
        
        # Get minimum profit threshold for this symbol
        min_profit = self._get_min_profit_for_symbol(symbol)
        
        # Compare all exchange pairs
        for price1, price2 in combinations(prices, 2):
            # Skip if prices are too old (more than 30 seconds)
            now = datetime.now()
            if (now - price1.timestamp).seconds > 30 or (now - price2.timestamp).seconds > 30:
                continue
            
            # Calculate potential arbitrage
            opportunity = self._calculate_arbitrage(price1, price2, min_profit)
            if opportunity:
                opportunities.append(opportunity)
        
        return opportunities
    
    def _calculate_arbitrage(self, price1: PriceData, price2: PriceData,
                           min_profit: float) -> Optional[ArbitrageSignal]:
        """Calculate arbitrage opportunity between two prices."""
        # Determine which exchange has lower/higher price
        if price1.price < price2.price:
            buy_price_data = price1
            sell_price_data = price2
        else:
            buy_price_data = price2
            sell_price_data = price1

        # Calculate profit
        price_diff = sell_price_data.price - buy_price_data.price
        profit_percent = (price_diff / buy_price_data.price) * 100

        # Check if profit meets minimum threshold
        if profit_percent < min_profit:
            return None

        # Get exchange configurations for enhanced calculations
        exchanges_config = config.exchanges
        buy_exchange_config = exchanges_config.get(buy_price_data.exchange.lower(), None)
        sell_exchange_config = exchanges_config.get(sell_price_data.exchange.lower(), None)

        # Calculate commission rates
        buy_commission = buy_exchange_config.commission_rate if buy_exchange_config else 0.001
        sell_commission = sell_exchange_config.commission_rate if sell_exchange_config else 0.001
        total_commission = (buy_commission + sell_commission) * 100  # Convert to percentage

        # Calculate position limits
        sell_limit = sell_exchange_config.position_limit if sell_exchange_config else 1000.0

        # Calculate deviations (simplified as random variation for now - can be enhanced)
        buy_deviation = round(profit_percent * 0.1, 2)  # 10% of profit as deviation
        sell_deviation = round(-profit_percent * 0.05, 2)  # 5% negative deviation

        # Calculate current profits (simplified)
        buy_current_profit = round(profit_percent * 0.01, 2)  # 1% of total profit
        sell_current_profit = round(profit_percent * 0.01, 2)  # 1% of total profit

        # Calculate exchange rate
        exchange_rate = round(profit_percent + total_commission, 2)

        # Period from config
        period = f"{config.bot.monitoring_interval}s"

        # Create arbitrage signal
        signal = ArbitrageSignal(
            symbol=buy_price_data.symbol,
            buy_exchange=buy_price_data.exchange,
            sell_exchange=sell_price_data.exchange,
            buy_price=buy_price_data.price,
            sell_price=sell_price_data.price,
            profit_percent=round(profit_percent, 2),
            profit_amount=round(price_diff, 6),
            action="LONG",  # Buy low, sell high
            timestamp=datetime.now(),
            sent_to_telegram=False,
            # Enhanced fields
            buy_current_profit=buy_current_profit,
            sell_current_profit=sell_current_profit,
            buy_deviation=buy_deviation,
            sell_deviation=sell_deviation,
            sell_limit=sell_limit,
            period=period,
            exchange_rate=exchange_rate,
            commission_rate=round(total_commission, 2)
        )

        logger.info(f"buy price {buy_price_data.price}, sell price {sell_price_data.price} ")

        return signal
    
    async def _validate_opportunity(self, signal: ArbitrageSignal) -> bool:
        """Validate if an arbitrage opportunity should be signaled."""
        # Check if similar signal was sent recently
        signal_key = f"{signal.symbol}_{signal.buy_exchange}_{signal.sell_exchange}"
        
        # Check database for recent similar signals
        recent_signal = await db_manager.check_recent_signal(
            signal.symbol,
            signal.buy_exchange,
            signal.sell_exchange,
            minutes=config.bot.max_signal_frequency // 60
        )
        
        if recent_signal:
            logger.debug(f"Skipping {signal_key} - similar signal sent recently")
            return False
        
        # Check minimum profit threshold
        min_profit = self._get_min_profit_for_symbol(signal.symbol)
        if signal.profit_percent < min_profit:
            return False
        
        # Additional validation: check if exchanges are different
        if signal.buy_exchange == signal.sell_exchange:
            return False
        
        # Additional validation: check if price difference is reasonable (not a data error)
        if signal.profit_percent > 50:  # More than 50% seems unrealistic
            logger.warning(f"Unusually high profit detected for {signal.symbol}: {signal.profit_percent}%")
            return False
        
        return True
    
    def _get_min_profit_for_symbol(self, symbol: str) -> float:
        """Get minimum profit threshold for a specific symbol."""
        trading_pairs = config.trading_pairs
        
        for pair in trading_pairs:
            if pair.symbol == symbol:
                return pair.min_profit
        
        # Default to global threshold
        return config.bot.profit_threshold

    async def analyze_realtime_opportunity(self, ticker_data_list: List[NormalizedTickerData]) -> List[ArbitrageSignal]:
        """
        Analyze real-time arbitrage opportunities from WebSocket ticker data.
        Uses advanced profit calculation with comprehensive fee analysis.
        """
        if len(ticker_data_list) < 2:
            return []

        opportunities = []
        symbol = ticker_data_list[0].symbol

        # Get minimum profit threshold for this symbol
        min_profit = self._get_min_profit_for_symbol(symbol)

        # Compare all exchange pairs
        for ticker1, ticker2 in combinations(ticker_data_list, 2):
            try:
                # Determine buy/sell exchanges based on prices
                if ticker1.ask < ticker2.bid:
                    buy_ticker = ticker1
                    sell_ticker = ticker2
                elif ticker2.ask < ticker1.bid:
                    buy_ticker = ticker2
                    sell_ticker = ticker1
                else:
                    continue  # No arbitrage opportunity

                # Calculate comprehensive profit
                profit_calc = profit_calculator.calculate_profit(
                    buy_ticker=buy_ticker,
                    sell_ticker=sell_ticker,
                    trade_amount_usd=100.0  # Default trade amount
                )

                # Check if opportunity meets criteria
                if (profit_calc.is_profitable and
                    profit_calc.profit_percent >= min_profit and
                    profit_calc.risk_score <= 0.7):  # Risk threshold

                    # Create enhanced arbitrage signal
                    signal = self._create_enhanced_signal(profit_calc)

                    # Validate the opportunity
                    if await self._validate_opportunity(signal):
                        opportunities.append(signal)

                        logger.info(
                            f"Real-time arbitrage opportunity: {symbol} "
                            f"Buy {buy_ticker.exchange}@{buy_ticker.ask:.6f} "
                            f"Sell {sell_ticker.exchange}@{sell_ticker.bid:.6f} "
                            f"Net profit: {profit_calc.profit_percent:.2f}% "
                            f"Risk: {profit_calc.risk_score:.2f}"
                        )

            except Exception as e:
                logger.error(f"Error analyzing real-time opportunity: {e}")
                continue

        return opportunities

    def _create_enhanced_signal(self, profit_calc: ProfitCalculation) -> ArbitrageSignal:
        """Create enhanced arbitrage signal from profit calculation."""
        return ArbitrageSignal(
            symbol=profit_calc.symbol,
            buy_exchange=profit_calc.buy_exchange,
            sell_exchange=profit_calc.sell_exchange,
            buy_price=profit_calc.buy_price,
            sell_price=profit_calc.sell_price,
            profit_percent=round(profit_calc.profit_percent, 2),
            profit_amount=round(profit_calc.net_profit, 6),
            action="LONG",
            timestamp=profit_calc.calculation_time,
            sent_to_telegram=False,
            # Enhanced fields with real calculations
            buy_current_profit=round(profit_calc.roi_percent * 0.5, 2),  # Estimated current profit
            sell_current_profit=round(profit_calc.roi_percent * 0.5, 2),
            buy_deviation=round(profit_calc.buy_slippage * 100, 2),  # Slippage as deviation
            sell_deviation=round(profit_calc.sell_slippage * 100, 2),
            sell_limit=profit_calc.trade_amount * profit_calc.sell_price,  # Trade value
            period=f"{config.bot.monitoring_interval}s",
            exchange_rate=round(profit_calc.profit_percent + (profit_calc.total_fees / profit_calc.trade_amount * profit_calc.buy_price * 100), 2),
            commission_rate=round((profit_calc.total_fees / (profit_calc.trade_amount * profit_calc.buy_price)) * 100, 2)
        )
    
    def _clean_price_cache(self):
        """Clean old entries from price cache."""
        current_time = datetime.now().timestamp()
        keys_to_remove = []
        
        for key in self.price_cache.keys():
            # Extract timestamp from cache key
            cache_time = float(key.split('_')[-1]) * self.cache_duration
            if current_time - cache_time > self.cache_duration * 2:
                keys_to_remove.append(key)
        
        for key in keys_to_remove:
            del self.price_cache[key]
    
    async def get_market_overview(self) -> Dict:
        """Get market overview with current prices and spreads."""
        overview = {
            'timestamp': datetime.now(),
            'exchanges': exchange_manager.get_connected_exchanges(),
            'symbols': {}
        }
        
        trading_pairs = config.trading_pairs
        enabled_symbols = [pair.symbol for pair in trading_pairs if pair.enabled]
        
        for symbol in enabled_symbols:
            try:
                prices = await exchange_manager.fetch_all_prices(symbol)
                if len(prices) >= 2:
                    # Calculate current spread
                    prices_by_exchange = {p.exchange: p.price for p in prices}
                    min_price = min(prices_by_exchange.values())
                    max_price = max(prices_by_exchange.values())
                    spread_percent = ((max_price - min_price) / min_price) * 100
                    
                    overview['symbols'][symbol] = {
                        'prices': prices_by_exchange,
                        'min_price': min_price,
                        'max_price': max_price,
                        'spread_percent': round(spread_percent, 2),
                        'last_update': datetime.now()
                    }
                    
            except Exception as e:
                logger.error(f"Error getting overview for {symbol}: {e}")
                continue
        
        return overview
    
    async def calculate_historical_performance(self, days: int = 7) -> Dict:
        """Calculate historical arbitrage performance."""
        since = datetime.now() - timedelta(days=days)
        
        performance = {
            'period_days': days,
            'total_signals': 0,
            'signals_by_symbol': {},
            'average_profit': 0.0,
            'best_opportunity': None,
            'exchange_pairs': {}
        }
        
        try:
            # Get all signals from the period
            all_signals = []
            trading_pairs = config.trading_pairs
            
            for pair in trading_pairs:
                if pair.enabled:
                    signals = await db_manager.get_recent_signals(pair.symbol, days * 24)
                    all_signals.extend(signals)
            
            if not all_signals:
                return performance
            
            performance['total_signals'] = len(all_signals)
            
            # Calculate statistics
            profits = [s.profit_percent for s in all_signals]
            performance['average_profit'] = round(sum(profits) / len(profits), 2)
            
            # Find best opportunity
            best_signal = max(all_signals, key=lambda s: s.profit_percent)
            performance['best_opportunity'] = {
                'symbol': best_signal.symbol,
                'profit_percent': best_signal.profit_percent,
                'buy_exchange': best_signal.buy_exchange,
                'sell_exchange': best_signal.sell_exchange,
                'timestamp': best_signal.timestamp
            }
            
            # Group by symbol
            for signal in all_signals:
                symbol = signal.symbol
                if symbol not in performance['signals_by_symbol']:
                    performance['signals_by_symbol'][symbol] = {
                        'count': 0,
                        'average_profit': 0.0,
                        'profits': []
                    }
                
                performance['signals_by_symbol'][symbol]['count'] += 1
                performance['signals_by_symbol'][symbol]['profits'].append(signal.profit_percent)
            
            # Calculate averages for each symbol
            for symbol_data in performance['signals_by_symbol'].values():
                if symbol_data['profits']:
                    symbol_data['average_profit'] = round(
                        sum(symbol_data['profits']) / len(symbol_data['profits']), 2
                    )
            
            # Group by exchange pairs
            for signal in all_signals:
                pair_key = f"{signal.buy_exchange}-{signal.sell_exchange}"
                if pair_key not in performance['exchange_pairs']:
                    performance['exchange_pairs'][pair_key] = {
                        'count': 0,
                        'average_profit': 0.0,
                        'profits': []
                    }
                
                performance['exchange_pairs'][pair_key]['count'] += 1
                performance['exchange_pairs'][pair_key]['profits'].append(signal.profit_percent)
            
            # Calculate averages for exchange pairs
            for pair_data in performance['exchange_pairs'].values():
                if pair_data['profits']:
                    pair_data['average_profit'] = round(
                        sum(pair_data['profits']) / len(pair_data['profits']), 2
                    )
            
        except Exception as e:
            logger.error(f"Error calculating historical performance: {e}")
        
        return performance


# Global arbitrage engine instance
arbitrage_engine = ArbitrageEngine()
