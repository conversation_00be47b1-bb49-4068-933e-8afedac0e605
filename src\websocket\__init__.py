"""
WebSocket module for real-time cryptocurrency exchange data.
"""

from .base import BaseWebSocketManager, WebSocketConfig, ConnectionState
from .data_normalizer import DataNormalizer, NormalizedTickerData
from .manager import WebSocketManager
from .exchange_websocket_manager import ExchangeWebSocketManager, exchange_websocket_manager

__all__ = [
    'BaseWebSocketManager',
    'WebSocketConfig',
    'ConnectionState',
    'DataNormalizer',
    'NormalizedTickerData',
    'WebSocketManager',
    'ExchangeWebSocketManager',
    'exchange_websocket_manager'
]
