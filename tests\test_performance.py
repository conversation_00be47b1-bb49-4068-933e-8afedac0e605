#!/usr/bin/env python3
"""
Performance tests to validate <100ms latency requirement and system optimization.
"""
import pytest
import asyncio
import time
import sys
from datetime import datetime
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.monitoring.performance_monitor import PerformanceMonitor, PerformanceMetrics
from src.monitoring.optimizer import PerformanceOptimizer, OptimizationLevel
from src.monitoring.dashboard import PerformanceDashboard
from src.websocket.data_normalizer import NormalizedTickerData
from src.profit.calculator import ProfitCalculator
from src.risk.manager import RiskManager


class TestPerformanceMonitor:
    """Test performance monitoring functionality."""
    
    @pytest.mark.asyncio
    async def test_performance_monitor_initialization(self):
        """Test performance monitor initialization."""
        monitor = PerformanceMonitor(alert_threshold_ms=50.0)
        
        assert monitor.alert_threshold_ms == 50.0
        assert monitor.is_monitoring is False
        assert len(monitor.metrics_history) == 0
        assert len(monitor.component_metrics) == 0
    
    @pytest.mark.asyncio
    async def test_latency_measurement(self):
        """Test latency measurement context manager."""
        monitor = PerformanceMonitor()
        
        async with monitor.measure_latency("test_component"):
            await asyncio.sleep(0.01)  # 10ms
        
        # Check that latency was recorded
        assert len(monitor.latency_samples) > 0
        assert monitor.latency_samples[-1] >= 10  # Should be at least 10ms
        
        # Check component metrics
        assert "test_component" in monitor.component_metrics
        component = monitor.component_metrics["test_component"]
        assert component.success_count == 1
        assert component.error_count == 0
        assert len(component.execution_times) == 1
    
    @pytest.mark.asyncio
    async def test_latency_measurement_with_error(self):
        """Test latency measurement with error handling."""
        monitor = PerformanceMonitor()
        
        try:
            async with monitor.measure_latency("error_component"):
                await asyncio.sleep(0.005)  # 5ms
                raise ValueError("Test error")
        except ValueError:
            pass
        
        # Check that error was recorded
        assert "error_component" in monitor.component_metrics
        component = monitor.component_metrics["error_component"]
        assert component.success_count == 0
        assert component.error_count == 1
        assert len(component.execution_times) == 1
    
    def test_record_message_processed(self):
        """Test message processing recording."""
        monitor = PerformanceMonitor()
        
        # Record some messages
        for _ in range(5):
            monitor.record_message_processed()
        
        # Check that messages were recorded
        assert len(monitor.message_counts) > 0
        assert sum(monitor.message_counts) >= 5
    
    def test_record_error(self):
        """Test error recording."""
        monitor = PerformanceMonitor()
        
        # Record some errors
        for _ in range(3):
            monitor.record_error()
        
        # Check that errors were recorded
        assert len(monitor.error_counts) > 0
        assert sum(monitor.error_counts) >= 3
    
    def test_get_performance_summary(self):
        """Test performance summary generation."""
        monitor = PerformanceMonitor()
        
        # Add some test data
        monitor.record_latency(50.0)
        monitor.record_latency(75.0)
        monitor.record_message_processed()
        monitor.record_error()
        
        summary = monitor.get_performance_summary()
        
        assert isinstance(summary, dict)
        assert 'uptime_seconds' in summary
        assert 'is_monitoring' in summary
        assert 'alert_threshold_ms' in summary
        assert 'average_latency_ms' in summary
        assert 'messages_per_second' in summary
        assert 'error_rate' in summary


class TestPerformanceOptimizer:
    """Test performance optimization functionality."""
    
    def test_optimizer_initialization(self):
        """Test optimizer initialization."""
        optimizer = PerformanceOptimizer(OptimizationLevel.CONSERVATIVE)
        
        assert optimizer.optimization_level == OptimizationLevel.CONSERVATIVE
        assert optimizer.is_optimizing is False
        assert len(optimizer.recommendations) == 0
        assert len(optimizer.optimization_results) == 0
        assert len(optimizer.tunable_parameters) > 0
    
    def test_tunable_parameters(self):
        """Test tunable parameters configuration."""
        optimizer = PerformanceOptimizer()
        
        # Check that all parameters have required fields
        for param_name, param_config in optimizer.tunable_parameters.items():
            assert 'min' in param_config
            assert 'max' in param_config
            assert 'current' in param_config
            assert param_config['min'] <= param_config['current'] <= param_config['max']
    
    @pytest.mark.asyncio
    async def test_collect_optimization_metrics(self):
        """Test optimization metrics collection."""
        optimizer = PerformanceOptimizer()
        
        with patch('src.monitoring.optimizer.performance_monitor') as mock_monitor:
            # Mock performance metrics
            mock_metrics = MagicMock()
            mock_metrics.latency_ms = 75.0
            mock_metrics.cpu_percent = 60.0
            mock_metrics.memory_mb = 400.0
            mock_metrics.messages_per_second = 100.0
            mock_metrics.error_rate = 2.0
            
            mock_monitor.get_current_metrics.return_value = mock_metrics
            mock_monitor.get_component_metrics.return_value = {}
            
            metrics = await optimizer._collect_optimization_metrics()
            
            assert isinstance(metrics, dict)
            assert metrics['latency_ms'] == 75.0
            assert metrics['cpu_percent'] == 60.0
            assert metrics['memory_mb'] == 400.0
            assert metrics['messages_per_second'] == 100.0
            assert metrics['error_rate'] == 2.0
    
    def test_analyze_latency_issues(self):
        """Test latency issue analysis."""
        optimizer = PerformanceOptimizer()
        
        # Test high latency
        metrics = {'latency_ms': 250.0}
        recommendations = optimizer._analyze_latency_issues(metrics)
        
        assert len(recommendations) > 0
        assert any('latency' in rec.issue.lower() for rec in recommendations)
        assert any(rec.priority >= 8 for rec in recommendations)
    
    def test_analyze_cpu_issues(self):
        """Test CPU issue analysis."""
        optimizer = PerformanceOptimizer()
        
        # Test high CPU
        metrics = {'cpu_percent': 85.0}
        recommendations = optimizer._analyze_cpu_issues(metrics)
        
        assert len(recommendations) > 0
        assert any('cpu' in rec.issue.lower() for rec in recommendations)
    
    def test_analyze_memory_issues(self):
        """Test memory issue analysis."""
        optimizer = PerformanceOptimizer()
        
        # Test high memory
        metrics = {'memory_mb': 1200.0}
        recommendations = optimizer._analyze_memory_issues(metrics)
        
        assert len(recommendations) > 0
        assert any('memory' in rec.issue.lower() for rec in recommendations)
    
    def test_get_max_optimizations_per_cycle(self):
        """Test optimization limits per cycle."""
        conservative = PerformanceOptimizer(OptimizationLevel.CONSERVATIVE)
        moderate = PerformanceOptimizer(OptimizationLevel.MODERATE)
        aggressive = PerformanceOptimizer(OptimizationLevel.AGGRESSIVE)
        
        assert conservative._get_max_optimizations_per_cycle() == 1
        assert moderate._get_max_optimizations_per_cycle() == 2
        assert aggressive._get_max_optimizations_per_cycle() == 3
    
    def test_get_optimization_summary(self):
        """Test optimization summary generation."""
        optimizer = PerformanceOptimizer()
        
        summary = optimizer.get_optimization_summary()
        
        assert isinstance(summary, dict)
        assert 'optimization_level' in summary
        assert 'is_optimizing' in summary
        assert 'total_optimizations_applied' in summary
        assert 'current_parameters' in summary


class TestPerformanceDashboard:
    """Test performance dashboard functionality."""
    
    def test_dashboard_initialization(self):
        """Test dashboard initialization."""
        dashboard = PerformanceDashboard()
        
        assert dashboard.is_running is False
        assert len(dashboard.dashboard_data) == 0
        assert dashboard.last_update_time is None
        assert dashboard.update_interval == 5.0
        assert len(dashboard.sections) > 0
    
    def test_get_dashboard_data(self):
        """Test dashboard data retrieval."""
        dashboard = PerformanceDashboard()
        
        # Set some test data
        dashboard.dashboard_data = {'test': 'data'}
        
        data = dashboard.get_dashboard_data()
        
        assert isinstance(data, dict)
        assert data == {'test': 'data'}
    
    def test_get_dashboard_json(self):
        """Test dashboard JSON serialization."""
        dashboard = PerformanceDashboard()
        
        # Set some test data
        dashboard.dashboard_data = {'test': 'data', 'timestamp': datetime.now()}
        
        json_str = dashboard.get_dashboard_json()
        
        assert isinstance(json_str, str)
        assert 'test' in json_str
        assert 'data' in json_str
    
    def test_calculate_trend(self):
        """Test trend calculation."""
        dashboard = PerformanceDashboard()
        
        # Test increasing trend
        increasing_values = [10, 15, 20, 25, 30, 35, 40]
        trend = dashboard._calculate_trend(increasing_values)
        assert trend == 'increasing'
        
        # Test decreasing trend
        decreasing_values = [40, 35, 30, 25, 20, 15, 10]
        trend = dashboard._calculate_trend(decreasing_values)
        assert trend == 'decreasing'
        
        # Test stable trend
        stable_values = [20, 21, 19, 20, 22, 19, 21]
        trend = dashboard._calculate_trend(stable_values)
        assert trend == 'stable'
    
    def test_get_component_status(self):
        """Test component status determination."""
        dashboard = PerformanceDashboard()
        
        # Mock component metrics
        healthy_component = MagicMock()
        healthy_component.error_rate = 1.0
        healthy_component.average_execution_time_ms = 50.0
        
        warning_component = MagicMock()
        warning_component.error_rate = 7.0
        warning_component.average_execution_time_ms = 80.0
        
        critical_component = MagicMock()
        critical_component.error_rate = 15.0
        critical_component.average_execution_time_ms = 200.0
        
        assert dashboard._get_component_status(healthy_component) == 'healthy'
        assert dashboard._get_component_status(warning_component) == 'warning'
        assert dashboard._get_component_status(critical_component) == 'critical'


class TestEndToEndPerformance:
    """End-to-end performance tests."""
    
    @pytest.mark.asyncio
    async def test_arbitrage_detection_latency(self):
        """Test that arbitrage detection meets <100ms latency requirement."""
        # Create test ticker data
        ticker1 = NormalizedTickerData(
            symbol="ADA/USDT",
            bid=0.4995,
            ask=0.5000,
            last=0.4998,
            volume=1000000,
            timestamp=datetime.now(),
            exchange="gate"
        )
        
        ticker2 = NormalizedTickerData(
            symbol="ADA/USDT",
            bid=0.5020,
            ask=0.5025,
            last=0.5022,
            volume=800000,
            timestamp=datetime.now(),
            exchange="mexc"
        )
        
        # Test profit calculation latency
        calculator = ProfitCalculator()
        
        start_time = time.perf_counter()
        profit_calc = calculator.calculate_profit(
            buy_ticker=ticker1,
            sell_ticker=ticker2,
            trade_amount_usd=100.0
        )
        end_time = time.perf_counter()
        
        latency_ms = (end_time - start_time) * 1000
        
        # Assert latency is under 100ms
        assert latency_ms < 100, f"Profit calculation took {latency_ms:.2f}ms, exceeds 100ms requirement"
        assert profit_calc is not None
    
    @pytest.mark.asyncio
    async def test_risk_assessment_latency(self):
        """Test that risk assessment meets latency requirements."""
        # Create test data
        risk_manager = RiskManager()
        
        # Mock profit calculation
        profit_calc = MagicMock()
        profit_calc.symbol = "ADA/USDT"
        profit_calc.buy_exchange = "gate"
        profit_calc.sell_exchange = "mexc"
        profit_calc.profit_percent = 2.0
        profit_calc.is_profitable = True
        profit_calc.calculation_time = datetime.now()
        profit_calc.risk_score = 0.3
        profit_calc.confidence = 0.8
        profit_calc.total_fees = 1.0
        profit_calc.total_slippage = 0.1
        profit_calc.trade_amount = 200.0
        profit_calc.buy_price = 0.5000
        
        # Mock ticker data
        ticker_data = [
            NormalizedTickerData(
                symbol="ADA/USDT",
                bid=0.4995,
                ask=0.5000,
                last=0.4998,
                volume=1000000,
                timestamp=datetime.now(),
                exchange="gate"
            )
        ]
        
        start_time = time.perf_counter()
        assessment = await risk_manager.assess_risk(
            profit_calc=profit_calc,
            ticker_data=ticker_data,
            trade_amount_usd=100.0
        )
        end_time = time.perf_counter()
        
        latency_ms = (end_time - start_time) * 1000
        
        # Assert latency is reasonable (should be much faster than profit calculation)
        assert latency_ms < 50, f"Risk assessment took {latency_ms:.2f}ms, exceeds 50ms target"
        assert assessment is not None
    
    @pytest.mark.asyncio
    async def test_data_processing_throughput(self):
        """Test data processing throughput."""
        monitor = PerformanceMonitor()
        
        # Simulate processing multiple messages
        message_count = 100
        start_time = time.perf_counter()
        
        for i in range(message_count):
            async with monitor.measure_latency("data_processing"):
                # Simulate data processing
                await asyncio.sleep(0.001)  # 1ms per message
        
        end_time = time.perf_counter()
        total_time = end_time - start_time
        throughput = message_count / total_time
        
        # Assert throughput is reasonable (should handle >50 messages/second)
        assert throughput > 50, f"Throughput {throughput:.1f} msg/s is too low"
        
        # Check that all measurements were recorded
        assert len(monitor.component_metrics["data_processing"].execution_times) == message_count


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
