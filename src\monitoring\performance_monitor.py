"""
Performance monitoring system for arbitrage bot with real-time metrics and optimization.
"""
import asyncio
import time
import psutil
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass, field
from collections import deque, defaultdict
from contextlib import asynccontextmanager
import statistics

from src.utils.logger import logger


@dataclass
class PerformanceMetrics:
    """Performance metrics data structure."""
    timestamp: datetime
    latency_ms: float
    cpu_percent: float
    memory_mb: float
    network_bytes_sent: int
    network_bytes_recv: int
    active_connections: int
    messages_per_second: float
    error_rate: float
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'timestamp': self.timestamp.isoformat(),
            'latency_ms': self.latency_ms,
            'cpu_percent': self.cpu_percent,
            'memory_mb': self.memory_mb,
            'network_bytes_sent': self.network_bytes_sent,
            'network_bytes_recv': self.network_bytes_recv,
            'active_connections': self.active_connections,
            'messages_per_second': self.messages_per_second,
            'error_rate': self.error_rate
        }


@dataclass
class ComponentMetrics:
    """Metrics for individual components."""
    name: str
    execution_times: deque = field(default_factory=lambda: deque(maxlen=1000))
    success_count: int = 0
    error_count: int = 0
    last_execution: Optional[datetime] = None
    
    @property
    def average_execution_time_ms(self) -> float:
        """Calculate average execution time in milliseconds."""
        if not self.execution_times:
            return 0.0
        return statistics.mean(self.execution_times)
    
    @property
    def p95_execution_time_ms(self) -> float:
        """Calculate 95th percentile execution time."""
        if not self.execution_times:
            return 0.0
        sorted_times = sorted(self.execution_times)
        index = int(0.95 * len(sorted_times))
        return sorted_times[min(index, len(sorted_times) - 1)]
    
    @property
    def error_rate(self) -> float:
        """Calculate error rate as percentage."""
        total = self.success_count + self.error_count
        if total == 0:
            return 0.0
        return (self.error_count / total) * 100
    
    @property
    def throughput_per_second(self) -> float:
        """Calculate throughput per second."""
        if not self.last_execution:
            return 0.0
        
        total_executions = self.success_count + self.error_count
        if total_executions == 0:
            return 0.0
        
        # Estimate based on recent activity
        time_window = min(60, len(self.execution_times))  # Last minute or available data
        if time_window == 0:
            return 0.0
        
        return total_executions / time_window


class PerformanceMonitor:
    """
    Comprehensive performance monitoring system with real-time metrics,
    alerting, and optimization recommendations.
    """
    
    def __init__(self, alert_threshold_ms: float = 100.0):
        self.alert_threshold_ms = alert_threshold_ms
        self.is_monitoring = False
        self.start_time = datetime.now()
        
        # Metrics storage
        self.metrics_history: deque = deque(maxlen=10000)  # Last 10k metrics
        self.component_metrics: Dict[str, ComponentMetrics] = {}
        
        # Performance tracking
        self.latency_samples: deque = deque(maxlen=1000)
        self.message_counts: deque = deque(maxlen=60)  # Last 60 seconds
        self.error_counts: deque = deque(maxlen=60)
        
        # System monitoring
        self.process = psutil.Process()
        self.network_stats_baseline = psutil.net_io_counters()
        
        # Alert callbacks
        self.alert_callbacks: List[Callable[[str, Dict], None]] = []
        
        # Monitoring task
        self.monitoring_task: Optional[asyncio.Task] = None
        
        # Thread-safe locks
        self._metrics_lock = threading.Lock()
        
    async def start_monitoring(self, interval_seconds: float = 1.0):
        """Start performance monitoring."""
        if self.is_monitoring:
            logger.warning("Performance monitoring already running")
            return
        
        self.is_monitoring = True
        self.start_time = datetime.now()
        
        # Initialize baseline metrics
        self._initialize_baseline()
        
        # Start monitoring task
        self.monitoring_task = asyncio.create_task(
            self._monitoring_loop(interval_seconds)
        )
        
        logger.info(f"Performance monitoring started with {interval_seconds}s interval")
    
    async def stop_monitoring(self):
        """Stop performance monitoring."""
        if not self.is_monitoring:
            return
        
        self.is_monitoring = False
        
        if self.monitoring_task and not self.monitoring_task.done():
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Performance monitoring stopped")
    
    def _initialize_baseline(self):
        """Initialize baseline metrics."""
        try:
            self.network_stats_baseline = psutil.net_io_counters()
        except Exception as e:
            logger.warning(f"Could not initialize network baseline: {e}")
            self.network_stats_baseline = None
    
    async def _monitoring_loop(self, interval_seconds: float):
        """Main monitoring loop."""
        while self.is_monitoring:
            try:
                await self._collect_metrics()
                await asyncio.sleep(interval_seconds)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(interval_seconds)
    
    async def _collect_metrics(self):
        """Collect current performance metrics."""
        try:
            current_time = datetime.now()
            
            # System metrics
            cpu_percent = self.process.cpu_percent()
            memory_info = self.process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            
            # Network metrics
            network_bytes_sent = 0
            network_bytes_recv = 0
            if self.network_stats_baseline:
                try:
                    current_net = psutil.net_io_counters()
                    network_bytes_sent = current_net.bytes_sent - self.network_stats_baseline.bytes_sent
                    network_bytes_recv = current_net.bytes_recv - self.network_stats_baseline.bytes_recv
                except Exception:
                    pass
            
            # Calculate derived metrics
            avg_latency = self._calculate_average_latency()
            messages_per_second = self._calculate_messages_per_second()
            error_rate = self._calculate_error_rate()
            active_connections = self._count_active_connections()
            
            # Create metrics object
            metrics = PerformanceMetrics(
                timestamp=current_time,
                latency_ms=avg_latency,
                cpu_percent=cpu_percent,
                memory_mb=memory_mb,
                network_bytes_sent=network_bytes_sent,
                network_bytes_recv=network_bytes_recv,
                active_connections=active_connections,
                messages_per_second=messages_per_second,
                error_rate=error_rate
            )
            
            # Store metrics
            with self._metrics_lock:
                self.metrics_history.append(metrics)
            
            # Check for alerts
            await self._check_alerts(metrics)
            
        except Exception as e:
            logger.error(f"Error collecting metrics: {e}")
    
    def _calculate_average_latency(self) -> float:
        """Calculate average latency from recent samples."""
        if not self.latency_samples:
            return 0.0
        return statistics.mean(self.latency_samples)
    
    def _calculate_messages_per_second(self) -> float:
        """Calculate messages per second."""
        if not self.message_counts:
            return 0.0
        return sum(self.message_counts) / len(self.message_counts)
    
    def _calculate_error_rate(self) -> float:
        """Calculate error rate percentage."""
        if not self.error_counts:
            return 0.0
        total_errors = sum(self.error_counts)
        total_messages = sum(self.message_counts) if self.message_counts else 1
        return (total_errors / max(total_messages, 1)) * 100
    
    def _count_active_connections(self) -> int:
        """Count active network connections."""
        try:
            connections = self.process.connections()
            return len([conn for conn in connections if conn.status == 'ESTABLISHED'])
        except Exception:
            return 0
    
    async def _check_alerts(self, metrics: PerformanceMetrics):
        """Check for performance alerts."""
        alerts = []
        
        # Latency alert
        if metrics.latency_ms > self.alert_threshold_ms:
            alerts.append({
                'type': 'high_latency',
                'message': f'High latency detected: {metrics.latency_ms:.2f}ms > {self.alert_threshold_ms}ms',
                'severity': 'warning' if metrics.latency_ms < self.alert_threshold_ms * 2 else 'critical',
                'value': metrics.latency_ms,
                'threshold': self.alert_threshold_ms
            })
        
        # CPU alert
        if metrics.cpu_percent > 80:
            alerts.append({
                'type': 'high_cpu',
                'message': f'High CPU usage: {metrics.cpu_percent:.1f}%',
                'severity': 'warning' if metrics.cpu_percent < 90 else 'critical',
                'value': metrics.cpu_percent,
                'threshold': 80
            })
        
        # Memory alert
        if metrics.memory_mb > 1000:  # 1GB
            alerts.append({
                'type': 'high_memory',
                'message': f'High memory usage: {metrics.memory_mb:.1f}MB',
                'severity': 'warning' if metrics.memory_mb < 2000 else 'critical',
                'value': metrics.memory_mb,
                'threshold': 1000
            })
        
        # Error rate alert
        if metrics.error_rate > 5:  # 5%
            alerts.append({
                'type': 'high_error_rate',
                'message': f'High error rate: {metrics.error_rate:.1f}%',
                'severity': 'warning' if metrics.error_rate < 10 else 'critical',
                'value': metrics.error_rate,
                'threshold': 5
            })
        
        # Send alerts
        for alert in alerts:
            await self._send_alert(alert)
    
    async def _send_alert(self, alert: Dict[str, Any]):
        """Send performance alert to registered callbacks."""
        for callback in self.alert_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(alert['type'], alert)
                else:
                    callback(alert['type'], alert)
            except Exception as e:
                logger.error(f"Error sending alert: {e}")
    
    @asynccontextmanager
    async def measure_latency(self, component_name: str):
        """Context manager to measure execution latency."""
        start_time = time.perf_counter()
        error_occurred = False
        
        try:
            yield
        except Exception as e:
            error_occurred = True
            raise
        finally:
            end_time = time.perf_counter()
            latency_ms = (end_time - start_time) * 1000
            
            # Record latency
            self.record_latency(latency_ms)
            
            # Update component metrics
            self.record_component_execution(component_name, latency_ms, not error_occurred)
    
    def record_latency(self, latency_ms: float):
        """Record a latency measurement."""
        with self._metrics_lock:
            self.latency_samples.append(latency_ms)
    
    def record_component_execution(self, component_name: str, execution_time_ms: float, success: bool):
        """Record component execution metrics."""
        if component_name not in self.component_metrics:
            self.component_metrics[component_name] = ComponentMetrics(name=component_name)
        
        component = self.component_metrics[component_name]
        component.execution_times.append(execution_time_ms)
        component.last_execution = datetime.now()
        
        if success:
            component.success_count += 1
        else:
            component.error_count += 1
    
    def record_message_processed(self):
        """Record that a message was processed."""
        current_second = int(time.time())
        
        with self._metrics_lock:
            # Ensure we have entries for the current second
            while len(self.message_counts) < 60:
                self.message_counts.append(0)
            
            # Update current second count
            self.message_counts[-1] += 1
            
            # Rotate if needed
            if len(self.message_counts) >= 60:
                self.message_counts.popleft()
                self.message_counts.append(0)
    
    def record_error(self):
        """Record that an error occurred."""
        current_second = int(time.time())
        
        with self._metrics_lock:
            # Ensure we have entries for the current second
            while len(self.error_counts) < 60:
                self.error_counts.append(0)
            
            # Update current second count
            self.error_counts[-1] += 1
            
            # Rotate if needed
            if len(self.error_counts) >= 60:
                self.error_counts.popleft()
                self.error_counts.append(0)
    
    def add_alert_callback(self, callback: Callable[[str, Dict], None]):
        """Add alert callback function."""
        self.alert_callbacks.append(callback)
    
    def get_current_metrics(self) -> Optional[PerformanceMetrics]:
        """Get the most recent performance metrics."""
        with self._metrics_lock:
            if self.metrics_history:
                return self.metrics_history[-1]
        return None
    
    def get_metrics_history(self, minutes: int = 60) -> List[PerformanceMetrics]:
        """Get metrics history for the specified time period."""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        
        with self._metrics_lock:
            return [
                metrics for metrics in self.metrics_history
                if metrics.timestamp >= cutoff_time
            ]
    
    def get_component_metrics(self) -> Dict[str, ComponentMetrics]:
        """Get metrics for all components."""
        return dict(self.component_metrics)
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary."""
        current_metrics = self.get_current_metrics()
        uptime = (datetime.now() - self.start_time).total_seconds()
        
        # Component summary
        component_summary = {}
        for name, component in self.component_metrics.items():
            component_summary[name] = {
                'average_execution_time_ms': component.average_execution_time_ms,
                'p95_execution_time_ms': component.p95_execution_time_ms,
                'error_rate': component.error_rate,
                'throughput_per_second': component.throughput_per_second,
                'total_executions': component.success_count + component.error_count,
                'last_execution': component.last_execution.isoformat() if component.last_execution else None
            }
        
        return {
            'uptime_seconds': uptime,
            'is_monitoring': self.is_monitoring,
            'alert_threshold_ms': self.alert_threshold_ms,
            'current_metrics': current_metrics.to_dict() if current_metrics else None,
            'component_metrics': component_summary,
            'total_metrics_collected': len(self.metrics_history),
            'average_latency_ms': self._calculate_average_latency(),
            'messages_per_second': self._calculate_messages_per_second(),
            'error_rate': self._calculate_error_rate()
        }


# Global instance
performance_monitor = PerformanceMonitor()
