#!/usr/bin/env python3
"""
Unit tests for ArbitrageSignal model and Telegram message template formatting.
Tests validate that the signal data is correctly formatted and no fields are null/wrong.
"""
import pytest
import sys
from datetime import datetime
from pathlib import Path
from unittest.mock import patch, MagicMock

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.database.models import ArbitrageSignal
from src.notifications.telegram_bot import TelegramNotifier
from src.config.settings import config


class TestArbitrageSignalTemplate:
    """Test suite for ArbitrageSignal model and template formatting."""
    
    def setup_method(self):
        """Set up test fixtures before each test method."""
        self.notifier = TelegramNotifier()
        self.valid_signal = self._create_valid_signal()
        
    def _create_valid_signal(self) -> ArbitrageSignal:
        """Create a valid ArbitrageSignal for testing."""
        return ArbitrageSignal(
            id=1,
            symbol="ADA/USDT",
            buy_exchange="gateio",
            sell_exchange="mexc",
            buy_price=8570.0,
            sell_price=8840.0,
            profit_percent=3.10,
            profit_amount=270.0,
            action="LONG",
            timestamp=datetime.now(),
            sent_to_telegram=False,
            # Enhanced fields
            buy_current_profit=0.01,
            sell_current_profit=0.01,
            buy_deviation=-2.97,
            sell_deviation=-0.01,
            period="4s",
            exchange_rate=3.10,
            commission_rate=0.14
        )
    
    def test_valid_signal_creation(self):
        """Test that a valid ArbitrageSignal can be created with all required fields."""
        signal = self.valid_signal
        
        # Test basic fields
        assert signal.symbol == "ADA/USDT"
        assert signal.buy_exchange == "gateio"
        assert signal.sell_exchange == "mexc"
        assert signal.buy_price == 8570.0
        assert signal.sell_price == 8840.0
        assert signal.profit_percent == 3.10
        assert signal.profit_amount == 270.0
        assert signal.action == "LONG"
        assert isinstance(signal.timestamp, datetime)
        assert signal.sent_to_telegram is False
        
        # Test enhanced fields
        assert signal.buy_current_profit == 0.01
        assert signal.sell_current_profit == 0.01
        assert signal.buy_deviation == -2.97
        assert signal.sell_deviation == -0.01
        assert signal.period == "4s"
        assert signal.exchange_rate == 3.10
        assert signal.commission_rate == 0.14
    
    def test_template_formatting_with_valid_data(self):
        """Test that template formatting works correctly with valid signal data."""
        signal = self.valid_signal
        
        # Format the message
        formatted_message = self.notifier._format_arbitrage_message(signal)
        
        # Verify the message is not empty
        assert formatted_message is not None
        assert len(formatted_message.strip()) > 0
        
        # Verify key components are present in the formatted message
        assert "gateio" in formatted_message.lower()
        assert "mexc" in formatted_message.lower()
        assert "UZUN" in formatted_message
        assert "KISA" in formatted_message
        assert "mevcut kar" in formatted_message.lower()
        assert "sapma" in formatted_message.lower()
        assert "dönem" in formatted_message.lower()
        assert "kur farkı analizi" in formatted_message.lower()
        assert "komisyon" in formatted_message.lower()
        
        # Verify numerical values are formatted correctly
        assert "0.01" in formatted_message  # current profits
        assert "-2.97" in formatted_message  # buy deviation
        assert "-0.01" in formatted_message  # sell deviation
        assert "4s" in formatted_message     # period
        assert "3.10" in formatted_message   # exchange rate and profit
        assert "8570" in formatted_message   # buy price
        assert "8840" in formatted_message   # sell price
        assert "0.14" in formatted_message   # commission rate
        assert "270" in formatted_message    # price diff
    
    def test_template_formatting_with_null_fields(self):
        """Test template formatting behavior when some fields are null/None."""
        signal = ArbitrageSignal(
            symbol="TEST/USDT",
            buy_exchange="exchange1",
            sell_exchange="exchange2",
            buy_price=100.0,
            sell_price=105.0,
            profit_percent=5.0,
            profit_amount=5.0,
            action="LONG",
            timestamp=datetime.now(),
            # Leave enhanced fields as defaults (0.0, empty strings)
        )
        
        # Should not raise an exception and should use fallback
        formatted_message = self.notifier._format_arbitrage_message(signal)
        
        assert formatted_message is not None
        assert len(formatted_message.strip()) > 0
        
        # Should contain basic exchange information
        assert "exchange1" in formatted_message.lower()
        assert "exchange2" in formatted_message.lower()
    
    def test_template_formatting_with_zero_values(self):
        """Test template formatting with zero values for enhanced fields."""
        signal = self.valid_signal
        signal.buy_current_profit = 0.0
        signal.sell_current_profit = 0.0
        signal.buy_deviation = 0.0
        signal.sell_deviation = 0.0
        signal.exchange_rate = 0.0
        signal.commission_rate = 0.0
        
        formatted_message = self.notifier._format_arbitrage_message(signal)
        
        assert formatted_message is not None
        assert "0.00" in formatted_message  # Zero values should be formatted
    
    def test_template_formatting_with_negative_values(self):
        """Test template formatting with negative values."""
        signal = self.valid_signal
        signal.buy_deviation = -5.25
        signal.sell_deviation = -1.33
        signal.profit_percent = -2.5  # Negative profit
        
        formatted_message = self.notifier._format_arbitrage_message(signal)
        
        assert formatted_message is not None
        assert "-5.25" in formatted_message
        assert "-1.33" in formatted_message
        assert "-2.50" in formatted_message
    
    def test_template_formatting_with_special_characters_in_symbol(self):
        """Test template formatting with special characters in symbol."""
        signal = self.valid_signal
        signal.symbol = "TEST-COIN/USDT"
        signal.buy_exchange = "exchange_with_underscore"
        signal.sell_exchange = "exchange-with-dash"
        
        formatted_message = self.notifier._format_arbitrage_message(signal)
        
        assert formatted_message is not None
        # HTML escaping should handle special characters safely
        assert "exchange_with_underscore" in formatted_message.lower()
        assert "exchange-with-dash" in formatted_message.lower()
    
    def test_fallback_message_creation(self):
        """Test that fallback message is created correctly when template fails."""
        signal = self.valid_signal
        
        fallback_message = self.notifier._create_fallback_message(signal)
        
        assert fallback_message is not None
        assert len(fallback_message.strip()) > 0
        
        # Verify fallback contains essential information
        assert "gateio" in fallback_message.lower()
        assert "mexc" in fallback_message.lower()
        assert "UZUN" in fallback_message
        assert "KISA" in fallback_message
        assert "3.10" in fallback_message
        assert "8570" in fallback_message
        assert "8840" in fallback_message
    
    def test_signal_data_types(self):
        """Test that all signal fields have correct data types."""
        signal = self.valid_signal
        
        # Test basic field types
        assert isinstance(signal.id, (int, type(None)))
        assert isinstance(signal.symbol, str)
        assert isinstance(signal.buy_exchange, str)
        assert isinstance(signal.sell_exchange, str)
        assert isinstance(signal.buy_price, (int, float))
        assert isinstance(signal.sell_price, (int, float))
        assert isinstance(signal.profit_percent, (int, float))
        assert isinstance(signal.profit_amount, (int, float))
        assert isinstance(signal.action, str)
        assert isinstance(signal.timestamp, (datetime, type(None)))
        assert isinstance(signal.sent_to_telegram, bool)
        
        # Test enhanced field types
        assert isinstance(signal.buy_current_profit, (int, float))
        assert isinstance(signal.sell_current_profit, (int, float))
        assert isinstance(signal.buy_deviation, (int, float))
        assert isinstance(signal.sell_deviation, (int, float))
        assert isinstance(signal.period, str)
        assert isinstance(signal.exchange_rate, (int, float))
        assert isinstance(signal.commission_rate, (int, float))
    
    def test_required_fields_not_empty(self):
        """Test that required fields are not empty or None."""
        signal = self.valid_signal
        
        # Critical fields that should never be empty
        assert signal.symbol is not None and signal.symbol.strip() != ""
        assert signal.buy_exchange is not None and signal.buy_exchange.strip() != ""
        assert signal.sell_exchange is not None and signal.sell_exchange.strip() != ""
        assert signal.action is not None and signal.action.strip() != ""
        assert signal.period is not None and signal.period.strip() != ""
        
        # Numerical fields should not be None (but can be 0)
        assert signal.buy_price is not None
        assert signal.sell_price is not None
        assert signal.profit_percent is not None
        assert signal.profit_amount is not None
    
    @patch('src.config.settings.config')
    def test_template_formatting_with_config_mock(self, mock_config):
        """Test template formatting with mocked configuration."""
        # Mock the telegram config
        mock_telegram_config = MagicMock()
        mock_telegram_config.message_template = """ 
            📈💸 <b>ARBITRAJ FIRSATI YAKALANDI!</b> 💸📈

            🔄 <b>Parite:</b> <code>{symbol}</code>
            💹 <b>Potansiyel Kar:</b> <code>{profit_percent}%</code>

            🟢 <b>AL (UZUN)</b> ➤ <b>{buy_exchange}</b> 
            💲 <b>Alış Fiyatı:</b> <code>${buy_price}</code>
            📈 <b>Mevcut Kar:</b> <code>%{buy_current_profit}</code>
            📉 <b>Sapma:</b> <code>%{buy_deviation}</code>
            🕒 <b>Dönem:</b> <code>{period}</code>

            🔴 <b>SAT (KISA)</b> ➤ <b>{sell_exchange}</b>
            💲 <b>Satış Fiyatı:</b> <code>${sell_price}</code>
            📈 <b>Mevcut Kar:</b> <code>%{sell_current_profit}</code>
            📉 <b>Sapma:</b> <code>%{sell_deviation}</code>
            🕒 <b>Dönem:</b> <code>{period}</code>

            📊 <b>Spread:</b> <code>${price_diff}</code>
            🌐 <b>Kur Farkı Analizi</b>:
              🔹 <b>Gerçek Kar:</b> <code>%{profit_percent}</code>
              🔸 <b>Kur Oranı:</b> <code>%{exchange_rate}</code>
              🧾 <b>Komisyon:</b> <code>%{commission_rate}</code>

            ⏱️ <b>Zaman:</b> <code>{timestamp}</code>
            📬 <i>Bu fırsat anlık hesaplama ile oluşturulmuştur. Lütfen teyit ederek işlem yapınız.</i>
        """
        
        mock_config.telegram = mock_telegram_config
        
        signal = self.valid_signal
        formatted_message = self.notifier._format_arbitrage_message(signal)
        
        assert formatted_message is not None
        assert "gateio" in formatted_message.lower()
        assert "mexc" in formatted_message.lower()
        assert "UZUN" in formatted_message
        assert "KISA" in formatted_message
        assert "Kur Farkı Analizi" in formatted_message


if __name__ == "__main__":
    pytest.main([__file__, "-v"])