#!/usr/bin/env python3
"""
Unit tests for WebSocket manager and data normalization.
"""
import pytest
import asyncio
import sys
from datetime import datetime
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.websocket.base import BaseWebSocketManager, WebSocketConfig, ConnectionState
from src.websocket.data_normalizer import DataNormalizer, NormalizedTickerData
from src.websocket.manager import WebSocketManager


class TestWebSocketConfig:
    """Test WebSocket configuration."""
    
    def test_default_config(self):
        """Test default WebSocket configuration."""
        config = WebSocketConfig("wss://test.com")
        
        assert config.url == "wss://test.com"
        assert config.ping_interval == 30
        assert config.ping_timeout == 10
        assert config.max_reconnect_attempts == 10
        assert config.initial_reconnect_delay == 1.0
        assert config.max_reconnect_delay == 60.0
        assert config.reconnect_backoff_factor == 2.0
        assert config.connection_timeout == 10
        assert config.message_timeout == 5
    
    def test_custom_config(self):
        """Test custom WebSocket configuration."""
        config = WebSocketConfig(
            url="wss://custom.com",
            ping_interval=60,
            max_reconnect_attempts=5
        )
        
        assert config.url == "wss://custom.com"
        assert config.ping_interval == 60
        assert config.max_reconnect_attempts == 5


class TestDataNormalizer:
    """Test data normalization functionality."""
    
    def test_normalize_gate_ticker(self):
        """Test Gate.io ticker normalization."""
        gate_data = {
            "method": "ticker.update",
            "params": [
                "ADA_USDT",
                {
                    "highest_bid": "0.5000",
                    "lowest_ask": "0.5010",
                    "last": "0.5005",
                    "base_volume": "1000000",
                    "high_24h": "0.5100",
                    "low_24h": "0.4900",
                    "change_percentage": "2.5"
                }
            ]
        }
        
        normalized = DataNormalizer.normalize_gate_ticker(gate_data, "ADA/USDT")
        
        assert normalized is not None
        assert normalized.symbol == "ADA/USDT"
        assert normalized.exchange == "gate"
        assert normalized.bid == 0.5000
        assert normalized.ask == 0.5010
        assert normalized.last == 0.5005
        assert normalized.volume == 1000000
        assert normalized.high == 0.5100
        assert normalized.low == 0.4900
        assert normalized.change_percent == 2.5
    
    def test_normalize_mexc_ticker(self):
        """Test MEXC ticker normalization."""
        mexc_data = {
            "stream": "ADAUSDT@ticker",
            "data": {
                "b": "0.5000",  # Best bid
                "a": "0.5010",  # Best ask
                "c": "0.5005",  # Last price
                "v": "1000000", # Volume
                "h": "0.5100",  # High
                "l": "0.4900",  # Low
                "o": "0.4950",  # Open
                "P": "1.11",    # Price change percent
                "E": *************  # Event time
            }
        }
        
        normalized = DataNormalizer.normalize_mexc_ticker(mexc_data, "ADA/USDT")
        
        assert normalized is not None
        assert normalized.symbol == "ADA/USDT"
        assert normalized.exchange == "mexc"
        assert normalized.bid == 0.5000
        assert normalized.ask == 0.5010
        assert normalized.last == 0.5005
        assert normalized.volume == 1000000
        assert normalized.high == 0.5100
        assert normalized.low == 0.4900
        assert normalized.open == 0.4950
        assert normalized.change_percent == 1.11
    
    def test_normalize_lbank_ticker(self):
        """Test LBank ticker normalization."""
        lbank_data = {
            "type": "ticker",
            "data": {
                "ticker": {
                    "buy": "0.5000",
                    "sell": "0.5010",
                    "latest": "0.5005",
                    "vol": "1000000",
                    "high": "0.5100",
                    "low": "0.4900",
                    "change": "0.0055"
                },
                "timestamp": *************
            }
        }
        
        normalized = DataNormalizer.normalize_lbank_ticker(lbank_data, "ADA/USDT")
        
        assert normalized is not None
        assert normalized.symbol == "ADA/USDT"
        assert normalized.exchange == "lbank"
        assert normalized.bid == 0.5000
        assert normalized.ask == 0.5010
        assert normalized.last == 0.5005
        assert normalized.volume == 1000000
        assert normalized.high == 0.5100
        assert normalized.low == 0.4900
    
    def test_validate_ticker_data_valid(self):
        """Test ticker data validation with valid data."""
        ticker = NormalizedTickerData(
            symbol="ADA/USDT",
            bid=0.5000,
            ask=0.5010,
            last=0.5005,
            volume=1000000,
            timestamp=datetime.now(),
            exchange="gate"
        )
        
        assert DataNormalizer.validate_ticker_data(ticker) is True
    
    def test_validate_ticker_data_invalid_prices(self):
        """Test ticker data validation with invalid prices."""
        ticker = NormalizedTickerData(
            symbol="ADA/USDT",
            bid=0.5010,  # Bid higher than ask
            ask=0.5000,
            last=0.5005,
            volume=1000000,
            timestamp=datetime.now(),
            exchange="gate"
        )
        
        assert DataNormalizer.validate_ticker_data(ticker) is False
    
    def test_validate_ticker_data_zero_prices(self):
        """Test ticker data validation with zero prices."""
        ticker = NormalizedTickerData(
            symbol="ADA/USDT",
            bid=0.0,  # Zero bid
            ask=0.5010,
            last=0.5005,
            volume=1000000,
            timestamp=datetime.now(),
            exchange="gate"
        )
        
        assert DataNormalizer.validate_ticker_data(ticker) is False
    
    def test_to_price_data(self):
        """Test conversion to PriceData model."""
        ticker = NormalizedTickerData(
            symbol="ADA/USDT",
            bid=0.5000,
            ask=0.5010,
            last=0.5005,
            volume=1000000,
            timestamp=datetime.now(),
            exchange="gate"
        )
        
        price_data = DataNormalizer.to_price_data(ticker)
        
        assert price_data.exchange == "gate"
        assert price_data.symbol == "ADA/USDT"
        assert price_data.price == 0.5005
        assert price_data.volume == 1000000
        assert price_data.bid == 0.5000
        assert price_data.ask == 0.5010


class TestNormalizedTickerData:
    """Test NormalizedTickerData functionality."""
    
    def test_mid_price_calculation(self):
        """Test mid price calculation."""
        ticker = NormalizedTickerData(
            symbol="ADA/USDT",
            bid=0.5000,
            ask=0.5010,
            last=0.5005,
            volume=1000000,
            timestamp=datetime.now(),
            exchange="gate"
        )
        
        assert ticker.mid_price == 0.5005
    
    def test_spread_calculation(self):
        """Test spread calculation."""
        ticker = NormalizedTickerData(
            symbol="ADA/USDT",
            bid=0.5000,
            ask=0.5010,
            last=0.5005,
            volume=1000000,
            timestamp=datetime.now(),
            exchange="gate"
        )
        
        assert ticker.spread == 0.0010
    
    def test_spread_percent_calculation(self):
        """Test spread percentage calculation."""
        ticker = NormalizedTickerData(
            symbol="ADA/USDT",
            bid=0.5000,
            ask=0.5010,
            last=0.5005,
            volume=1000000,
            timestamp=datetime.now(),
            exchange="gate"
        )
        
        expected_spread_percent = (0.0010 / 0.5005) * 100
        assert abs(ticker.spread_percent - expected_spread_percent) < 0.001


class MockWebSocketManager(BaseWebSocketManager):
    """Mock WebSocket manager for testing."""
    
    def __init__(self, exchange_name: str, config: WebSocketConfig):
        super().__init__(exchange_name, config)
        self.connected_calls = 0
        self.message_calls = 0
        self.ping_calls = 0
    
    async def _on_connected(self):
        self.connected_calls += 1
    
    async def _on_message(self, data):
        self.message_calls += 1
    
    async def _send_ping(self):
        self.ping_calls += 1
    
    def _is_pong_message(self, data) -> bool:
        return data.get("type") == "pong"
    
    def _get_message_type(self, data) -> str:
        return data.get("type", "unknown")


class TestWebSocketManager:
    """Test WebSocket manager functionality."""
    
    def test_manager_initialization(self):
        """Test WebSocket manager initialization."""
        manager = WebSocketManager()
        
        assert len(manager.connections) == 0
        assert len(manager.data_handlers) == 0
        assert len(manager.error_handlers) == 0
        assert manager.is_running is False
    
    def test_add_connection(self):
        """Test adding WebSocket connection."""
        manager = WebSocketManager()
        config = WebSocketConfig("wss://test.com")
        mock_connection = MockWebSocketManager("test_exchange", config)
        
        manager.add_connection("test_exchange", mock_connection)
        
        assert "test_exchange" in manager.connections
        assert manager.connections["test_exchange"] == mock_connection
    
    def test_add_handlers(self):
        """Test adding data and error handlers."""
        manager = WebSocketManager()
        
        def data_handler(data):
            pass
        
        def error_handler(exchange, error):
            pass
        
        manager.add_data_handler(data_handler)
        manager.add_error_handler(error_handler)
        
        assert len(manager.data_handlers) == 1
        assert len(manager.error_handlers) == 1
    
    def test_get_latest_data_empty(self):
        """Test getting latest data when no data available."""
        manager = WebSocketManager()
        
        result = manager.get_latest_data("ADA/USDT")
        
        assert result == {}
    
    def test_get_connected_exchanges_empty(self):
        """Test getting connected exchanges when none connected."""
        manager = WebSocketManager()
        
        result = manager.get_connected_exchanges()
        
        assert result == []


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
