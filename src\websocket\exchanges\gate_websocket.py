"""
Gate.io WebSocket implementation for real-time ticker data.
"""
import json
from typing import Dict, Any, List
from datetime import datetime

from src.websocket.base import BaseWebSocketManager, WebSocketConfig
from src.websocket.data_normalizer import <PERSON>Normali<PERSON>, NormalizedTickerData
from src.utils.logger import logger


class GateWebSocketManager(BaseWebSocketManager):
    """Gate.io WebSocket manager for ticker data."""
    
    def __init__(self, config: WebSocketConfig):
        super().__init__("gate", config)
        self.subscribed_symbols: List[str] = []
        self.request_id = 1
    
    async def _on_connected(self):
        """Called when WebSocket connection is established."""
        logger.info(f"{self.exchange_name}: WebSocket connected, ready for subscriptions")
    
    async def _on_message(self, data: Dict[str, Any]):
        """Handle incoming WebSocket message."""
        try:
            # Gate.io sends different message types
            if "method" in data:
                method = data["method"]
                
                if method == "ticker.update":
                    await self._handle_ticker_update(data)
                elif method == "server.ping":
                    await self._handle_ping(data)
                else:
                    logger.debug(f"{self.exchange_name}: Unhandled method: {method}")
            
            elif "result" in data:
                # Response to subscription request
                await self._handle_subscription_response(data)
            
            elif "error" in data:
                logger.error(f"{self.exchange_name}: WebSocket error: {data['error']}")
            
        except Exception as e:
            logger.error(f"{self.exchange_name}: Error processing message: {e}")
    
    async def _send_ping(self):
        """Send ping message to maintain connection."""
        ping_message = {
            "id": self.request_id,
            "method": "server.ping",
            "params": []
        }
        self.request_id += 1
        await self.send_message(ping_message)
    
    def _is_pong_message(self, data: Dict[str, Any]) -> bool:
        """Check if message is a pong response."""
        return data.get("method") == "server.pong"
    
    def _get_message_type(self, data: Dict[str, Any]) -> str:
        """Extract message type from received data."""
        if "method" in data:
            return data["method"]
        elif "result" in data:
            return "result"
        elif "error" in data:
            return "error"
        return "unknown"
    
    async def subscribe_ticker(self, symbol: str) -> bool:
        """Subscribe to ticker updates for a symbol."""
        try:
            # Convert symbol format (e.g., "ADA/USDT" -> "ADA_USDT")
            gate_symbol = symbol.replace("/", "_")
            
            subscription_message = {
                "id": self.request_id,
                "method": "ticker.subscribe",
                "params": [gate_symbol]
            }
            self.request_id += 1
            
            success = await self.send_message(subscription_message)
            if success:
                self.subscribed_symbols.append(symbol)
                logger.info(f"{self.exchange_name}: Subscribed to ticker for {symbol}")
            
            return success
            
        except Exception as e:
            logger.error(f"{self.exchange_name}: Failed to subscribe to {symbol}: {e}")
            return False
    
    async def unsubscribe_ticker(self, symbol: str) -> bool:
        """Unsubscribe from ticker updates for a symbol."""
        try:
            gate_symbol = symbol.replace("/", "_")
            
            unsubscription_message = {
                "id": self.request_id,
                "method": "ticker.unsubscribe",
                "params": [gate_symbol]
            }
            self.request_id += 1
            
            success = await self.send_message(unsubscription_message)
            if success and symbol in self.subscribed_symbols:
                self.subscribed_symbols.remove(symbol)
                logger.info(f"{self.exchange_name}: Unsubscribed from ticker for {symbol}")
            
            return success
            
        except Exception as e:
            logger.error(f"{self.exchange_name}: Failed to unsubscribe from {symbol}: {e}")
            return False
    
    async def _handle_ticker_update(self, data: Dict[str, Any]):
        """Handle ticker update message."""
        try:
            if "params" in data and len(data["params"]) >= 2:
                gate_symbol = data["params"][0]
                ticker_data = data["params"][1]
                
                # Convert back to standard format
                symbol = gate_symbol.replace("_", "/")
                
                # Normalize the data
                normalized_data = DataNormalizer.normalize_gate_ticker(data, symbol)
                
                if normalized_data and DataNormalizer.validate_ticker_data(normalized_data):
                    # Store in database
                    price_data = DataNormalizer.to_price_data(normalized_data)
                    await self._store_price_data(price_data)
                    
                    # Notify handlers
                    await self._notify_data_handlers(normalized_data)
                    
                    logger.debug(f"{self.exchange_name}: Processed ticker for {symbol}")
                else:
                    logger.warning(f"{self.exchange_name}: Invalid ticker data for {symbol}")
        
        except Exception as e:
            logger.error(f"{self.exchange_name}: Error handling ticker update: {e}")
    
    async def _handle_ping(self, data: Dict[str, Any]):
        """Handle server ping message."""
        try:
            pong_message = {
                "id": data.get("id"),
                "method": "server.pong",
                "params": []
            }
            await self.send_message(pong_message)
            logger.debug(f"{self.exchange_name}: Responded to server ping")
        
        except Exception as e:
            logger.error(f"{self.exchange_name}: Error responding to ping: {e}")
    
    async def _handle_subscription_response(self, data: Dict[str, Any]):
        """Handle subscription response."""
        try:
            result = data.get("result")
            request_id = data.get("id")
            
            if result is not None:
                if result.get("status") == "success":
                    logger.debug(f"{self.exchange_name}: Subscription successful (ID: {request_id})")
                else:
                    logger.warning(f"{self.exchange_name}: Subscription failed (ID: {request_id}): {result}")
            
        except Exception as e:
            logger.error(f"{self.exchange_name}: Error handling subscription response: {e}")
    
    async def _store_price_data(self, price_data):
        """Store price data in database."""
        try:
            from src.database.models import db_manager
            await db_manager.insert_price(price_data)
        except Exception as e:
            logger.error(f"{self.exchange_name}: Failed to store price data: {e}")
    
    async def _notify_data_handlers(self, normalized_data: NormalizedTickerData):
        """Notify registered data handlers."""
        # Call the data handler if set by the manager
        if hasattr(self, '_data_handler') and self._data_handler:
            try:
                await self._data_handler(normalized_data)
            except Exception as e:
                logger.error(f"{self.exchange_name}: Error in data handler: {e}")

    def set_data_handler(self, handler):
        """Set the data handler callback."""
        self._data_handler = handler


def create_gate_websocket(exchange_config) -> GateWebSocketManager:
    """Create Gate.io WebSocket manager with configuration."""
    # Gate.io WebSocket URL
    ws_url = "wss://api.gateio.ws/ws/v4/"
    
    config = WebSocketConfig(
        url=ws_url,
        ping_interval=30,
        ping_timeout=10,
        max_reconnect_attempts=10,
        initial_reconnect_delay=1.0,
        max_reconnect_delay=60.0,
        reconnect_backoff_factor=2.0,
        connection_timeout=exchange_config.timeout,
        message_timeout=5
    )
    
    return GateWebSocketManager(config)
