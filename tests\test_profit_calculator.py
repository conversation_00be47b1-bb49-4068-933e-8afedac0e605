#!/usr/bin/env python3
"""
Unit tests for profit calculation engine.
"""
import pytest
import sys
from datetime import datetime
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.profit.calculator import ProfitCalculator, FeeStructure, SlippageEstimate, ProfitCalculation
from src.websocket.data_normalizer import NormalizedTickerData


class TestFeeStructure:
    """Test fee structure functionality."""
    
    def test_default_fee_structure(self):
        """Test default fee structure values."""
        fees = FeeStructure()
        
        assert fees.maker_fee == 0.001
        assert fees.taker_fee == 0.001
        assert fees.withdrawal_fee == 0.0
        assert fees.withdrawal_fee_percent == 0.0
        assert fees.minimum_trade == 10.0
        assert fees.network_fee == 0.0
    
    def test_custom_fee_structure(self):
        """Test custom fee structure values."""
        fees = FeeStructure(
            maker_fee=0.0005,
            taker_fee=0.001,
            withdrawal_fee=1.0,
            minimum_trade=5.0
        )
        
        assert fees.maker_fee == 0.0005
        assert fees.taker_fee == 0.001
        assert fees.withdrawal_fee == 1.0
        assert fees.minimum_trade == 5.0


class TestSlippageEstimate:
    """Test slippage estimation functionality."""
    
    def test_slippage_estimate_creation(self):
        """Test slippage estimate creation."""
        estimate = SlippageEstimate(
            buy_slippage=0.001,
            sell_slippage=0.0015,
            confidence=0.8,
            orderbook_depth=10000.0
        )
        
        assert estimate.buy_slippage == 0.001
        assert estimate.sell_slippage == 0.0015
        assert estimate.confidence == 0.8
        assert estimate.orderbook_depth == 10000.0


class TestProfitCalculation:
    """Test profit calculation result functionality."""
    
    def test_profit_calculation_creation(self):
        """Test profit calculation creation."""
        calc = ProfitCalculation(
            symbol="ADA/USDT",
            buy_exchange="gate",
            sell_exchange="mexc",
            buy_price=0.5000,
            sell_price=0.5020,
            trade_amount=200.0,
            buy_trading_fee=0.10,
            sell_trading_fee=0.10,
            withdrawal_fee=1.0,
            network_fee=0.1,
            total_fees=1.3,
            buy_slippage=0.05,
            sell_slippage=0.05,
            total_slippage=0.10,
            gross_profit=4.0,
            net_profit=2.6,
            profit_percent=2.6,
            roi_percent=2.6,
            risk_score=0.3,
            confidence=0.8,
            calculation_time=datetime.now(),
            is_profitable=True
        )
        
        assert calc.symbol == "ADA/USDT"
        assert calc.buy_exchange == "gate"
        assert calc.sell_exchange == "mexc"
        assert calc.is_profitable is True
        assert calc.net_profit == 2.6
        assert calc.profit_percent == 2.6
    
    def test_profit_calculation_to_dict(self):
        """Test profit calculation dictionary conversion."""
        calc = ProfitCalculation(
            symbol="ADA/USDT",
            buy_exchange="gate",
            sell_exchange="mexc",
            buy_price=0.5000,
            sell_price=0.5020,
            trade_amount=200.0,
            buy_trading_fee=0.10,
            sell_trading_fee=0.10,
            withdrawal_fee=1.0,
            network_fee=0.1,
            total_fees=1.3,
            buy_slippage=0.05,
            sell_slippage=0.05,
            total_slippage=0.10,
            gross_profit=4.0,
            net_profit=2.6,
            profit_percent=2.6,
            roi_percent=2.6,
            risk_score=0.3,
            confidence=0.8,
            calculation_time=datetime.now(),
            is_profitable=True
        )
        
        result = calc.to_dict()
        
        assert isinstance(result, dict)
        assert result['symbol'] == "ADA/USDT"
        assert result['buy_exchange'] == "gate"
        assert result['sell_exchange'] == "mexc"
        assert result['is_profitable'] is True
        assert 'calculation_time' in result


class TestProfitCalculator:
    """Test profit calculator functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.calculator = ProfitCalculator()
        
        # Create test ticker data
        self.buy_ticker = NormalizedTickerData(
            symbol="ADA/USDT",
            bid=0.4995,
            ask=0.5000,
            last=0.4998,
            volume=1000000,
            timestamp=datetime.now(),
            exchange="gate"
        )
        
        self.sell_ticker = NormalizedTickerData(
            symbol="ADA/USDT",
            bid=0.5020,
            ask=0.5025,
            last=0.5022,
            volume=800000,
            timestamp=datetime.now(),
            exchange="mexc"
        )
    
    def test_calculator_initialization(self):
        """Test profit calculator initialization."""
        calculator = ProfitCalculator()
        
        assert len(calculator.fee_structures) > 0
        assert len(calculator.slippage_models) > 0
    
    def test_fee_structure_loading(self):
        """Test fee structure loading."""
        calculator = ProfitCalculator()
        
        # Should have fee structures for configured exchanges
        assert 'gate' in calculator.fee_structures
        assert 'mexc' in calculator.fee_structures
        assert 'lbank' in calculator.fee_structures
        
        # Check fee structure properties
        gate_fees = calculator.fee_structures['gate']
        assert isinstance(gate_fees, FeeStructure)
        assert gate_fees.maker_fee > 0
        assert gate_fees.taker_fee > 0
    
    def test_withdrawal_fee_calculation(self):
        """Test withdrawal fee calculation."""
        calculator = ProfitCalculator()
        
        gate_fee = calculator._get_withdrawal_fee('gate')
        mexc_fee = calculator._get_withdrawal_fee('mexc')
        unknown_fee = calculator._get_withdrawal_fee('unknown')
        
        assert gate_fee >= 0
        assert mexc_fee >= 0
        assert unknown_fee >= 0  # Should have default
    
    def test_minimum_trade_calculation(self):
        """Test minimum trade amount calculation."""
        calculator = ProfitCalculator()
        
        gate_min = calculator._get_minimum_trade('gate')
        mexc_min = calculator._get_minimum_trade('mexc')
        unknown_min = calculator._get_minimum_trade('unknown')
        
        assert gate_min > 0
        assert mexc_min > 0
        assert unknown_min > 0  # Should have default
    
    def test_network_fee_calculation(self):
        """Test network fee calculation."""
        calculator = ProfitCalculator()
        
        gate_network = calculator._get_network_fee('gate')
        mexc_network = calculator._get_network_fee('mexc')
        unknown_network = calculator._get_network_fee('unknown')
        
        assert gate_network >= 0
        assert mexc_network >= 0
        assert unknown_network >= 0  # Should have default
    
    def test_slippage_estimation(self):
        """Test slippage estimation."""
        calculator = ProfitCalculator()
        
        buy_estimate = calculator._estimate_slippage(self.buy_ticker, 100.0, 'buy')
        sell_estimate = calculator._estimate_slippage(self.sell_ticker, 100.0, 'sell')
        
        assert isinstance(buy_estimate, SlippageEstimate)
        assert isinstance(sell_estimate, SlippageEstimate)
        assert buy_estimate.buy_slippage > 0
        assert sell_estimate.sell_slippage > 0
        assert 0 <= buy_estimate.confidence <= 1
        assert 0 <= sell_estimate.confidence <= 1
    
    def test_ticker_validation_valid(self):
        """Test ticker validation with valid data."""
        calculator = ProfitCalculator()
        
        result = calculator._validate_tickers(self.buy_ticker, self.sell_ticker)
        
        assert result is True
    
    def test_ticker_validation_same_symbol(self):
        """Test ticker validation with different symbols."""
        calculator = ProfitCalculator()
        
        different_symbol_ticker = NormalizedTickerData(
            symbol="BTC/USDT",  # Different symbol
            bid=30000,
            ask=30010,
            last=30005,
            volume=100,
            timestamp=datetime.now(),
            exchange="mexc"
        )
        
        result = calculator._validate_tickers(self.buy_ticker, different_symbol_ticker)
        
        assert result is False
    
    def test_ticker_validation_same_exchange(self):
        """Test ticker validation with same exchange."""
        calculator = ProfitCalculator()
        
        same_exchange_ticker = NormalizedTickerData(
            symbol="ADA/USDT",
            bid=0.5020,
            ask=0.5025,
            last=0.5022,
            volume=800000,
            timestamp=datetime.now(),
            exchange="gate"  # Same exchange as buy_ticker
        )
        
        result = calculator._validate_tickers(self.buy_ticker, same_exchange_ticker)
        
        assert result is False
    
    def test_ticker_validation_zero_prices(self):
        """Test ticker validation with zero prices."""
        calculator = ProfitCalculator()
        
        zero_price_ticker = NormalizedTickerData(
            symbol="ADA/USDT",
            bid=0.0,  # Zero bid
            ask=0.5025,
            last=0.5022,
            volume=800000,
            timestamp=datetime.now(),
            exchange="mexc"
        )
        
        result = calculator._validate_tickers(self.buy_ticker, zero_price_ticker)
        
        assert result is False
    
    def test_profit_calculation_profitable(self):
        """Test profit calculation with profitable opportunity."""
        calculator = ProfitCalculator()
        
        result = calculator.calculate_profit(
            buy_ticker=self.buy_ticker,
            sell_ticker=self.sell_ticker,
            trade_amount_usd=100.0
        )
        
        assert isinstance(result, ProfitCalculation)
        assert result.symbol == "ADA/USDT"
        assert result.buy_exchange == "gate"
        assert result.sell_exchange == "mexc"
        assert result.buy_price == self.buy_ticker.ask
        assert result.sell_price == self.sell_ticker.bid
        assert result.trade_amount > 0
        assert result.total_fees > 0
        assert result.gross_profit > 0
        # Net profit might be negative due to fees
        assert isinstance(result.is_profitable, bool)
    
    def test_profit_calculation_with_different_amounts(self):
        """Test profit calculation with different trade amounts."""
        calculator = ProfitCalculator()
        
        result_100 = calculator.calculate_profit(
            buy_ticker=self.buy_ticker,
            sell_ticker=self.sell_ticker,
            trade_amount_usd=100.0
        )
        
        result_1000 = calculator.calculate_profit(
            buy_ticker=self.buy_ticker,
            sell_ticker=self.sell_ticker,
            trade_amount_usd=1000.0
        )
        
        # Larger trade should have proportionally larger amounts
        assert result_1000.trade_amount > result_100.trade_amount
        assert result_1000.gross_profit > result_100.gross_profit
        # But fees might not scale linearly due to fixed withdrawal fees
    
    def test_risk_score_calculation(self):
        """Test risk score calculation."""
        calculator = ProfitCalculator()
        
        buy_slippage = SlippageEstimate(buy_slippage=0.001, confidence=0.8)
        sell_slippage = SlippageEstimate(sell_slippage=0.001, confidence=0.8)
        
        risk_score = calculator._calculate_risk_score(
            self.buy_ticker, self.sell_ticker, buy_slippage, sell_slippage
        )
        
        assert 0 <= risk_score <= 1
    
    def test_invalid_ticker_calculation(self):
        """Test profit calculation with invalid ticker data."""
        calculator = ProfitCalculator()
        
        invalid_ticker = NormalizedTickerData(
            symbol="BTC/USDT",  # Different symbol
            bid=30000,
            ask=30010,
            last=30005,
            volume=100,
            timestamp=datetime.now(),
            exchange="mexc"
        )
        
        with pytest.raises(ValueError):
            calculator.calculate_profit(
                buy_ticker=self.buy_ticker,
                sell_ticker=invalid_ticker,
                trade_amount_usd=100.0
            )


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
