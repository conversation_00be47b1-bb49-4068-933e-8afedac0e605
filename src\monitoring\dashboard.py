"""
Real-time performance monitoring dashboard with metrics visualization.
"""
import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import asdict

from src.monitoring.performance_monitor import performance_monitor
from src.monitoring.optimizer import performance_optimizer
from src.risk.manager import risk_manager
from src.utils.logger import logger


class PerformanceDashboard:
    """
    Real-time performance dashboard that aggregates metrics from all components
    and provides comprehensive monitoring data.
    """
    
    def __init__(self):
        self.is_running = False
        self.dashboard_data: Dict[str, Any] = {}
        self.last_update_time: Optional[datetime] = None
        self.update_interval = 5.0  # seconds
        
        # Dashboard sections
        self.sections = [
            'system_overview',
            'latency_metrics',
            'component_performance',
            'websocket_status',
            'risk_management',
            'optimization_status',
            'alerts_and_warnings'
        ]
    
    async def start_dashboard(self, update_interval: float = 5.0):
        """Start the performance dashboard."""
        if self.is_running:
            logger.warning("Performance dashboard already running")
            return
        
        self.is_running = True
        self.update_interval = update_interval
        
        logger.info(f"Performance dashboard started with {update_interval}s update interval")
        
        # Start dashboard update loop
        asyncio.create_task(self._dashboard_update_loop())
    
    async def stop_dashboard(self):
        """Stop the performance dashboard."""
        self.is_running = False
        logger.info("Performance dashboard stopped")
    
    async def _dashboard_update_loop(self):
        """Main dashboard update loop."""
        while self.is_running:
            try:
                await self._update_dashboard_data()
                await asyncio.sleep(self.update_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error updating dashboard: {e}")
                await asyncio.sleep(self.update_interval)
    
    async def _update_dashboard_data(self):
        """Update all dashboard data."""
        try:
            self.dashboard_data = {
                'timestamp': datetime.now().isoformat(),
                'system_overview': await self._get_system_overview(),
                'latency_metrics': await self._get_latency_metrics(),
                'component_performance': await self._get_component_performance(),
                'websocket_status': await self._get_websocket_status(),
                'risk_management': await self._get_risk_management_status(),
                'optimization_status': await self._get_optimization_status(),
                'alerts_and_warnings': await self._get_alerts_and_warnings()
            }
            
            self.last_update_time = datetime.now()
            
        except Exception as e:
            logger.error(f"Error updating dashboard data: {e}")
    
    async def _get_system_overview(self) -> Dict[str, Any]:
        """Get system overview metrics."""
        current_metrics = performance_monitor.get_current_metrics()
        performance_summary = performance_monitor.get_performance_summary()
        
        if current_metrics:
            return {
                'status': 'healthy' if current_metrics.latency_ms < 100 else 'warning',
                'uptime_seconds': performance_summary.get('uptime_seconds', 0),
                'cpu_percent': current_metrics.cpu_percent,
                'memory_mb': current_metrics.memory_mb,
                'network_bytes_sent': current_metrics.network_bytes_sent,
                'network_bytes_recv': current_metrics.network_bytes_recv,
                'active_connections': current_metrics.active_connections,
                'messages_per_second': current_metrics.messages_per_second,
                'error_rate': current_metrics.error_rate,
                'total_metrics_collected': performance_summary.get('total_metrics_collected', 0)
            }
        
        return {
            'status': 'unknown',
            'uptime_seconds': 0,
            'cpu_percent': 0,
            'memory_mb': 0,
            'network_bytes_sent': 0,
            'network_bytes_recv': 0,
            'active_connections': 0,
            'messages_per_second': 0,
            'error_rate': 0,
            'total_metrics_collected': 0
        }
    
    async def _get_latency_metrics(self) -> Dict[str, Any]:
        """Get detailed latency metrics."""
        current_metrics = performance_monitor.get_current_metrics()
        history = performance_monitor.get_metrics_history(minutes=60)
        
        latency_data = {
            'current_latency_ms': current_metrics.latency_ms if current_metrics else 0,
            'target_latency_ms': 100,
            'status': 'good' if (current_metrics and current_metrics.latency_ms < 100) else 'warning'
        }
        
        if history:
            latencies = [m.latency_ms for m in history]
            latency_data.update({
                'average_latency_ms': sum(latencies) / len(latencies),
                'min_latency_ms': min(latencies),
                'max_latency_ms': max(latencies),
                'p95_latency_ms': sorted(latencies)[int(0.95 * len(latencies))] if latencies else 0,
                'latency_trend': self._calculate_trend(latencies),
                'samples_count': len(latencies)
            })
        
        return latency_data
    
    async def _get_component_performance(self) -> Dict[str, Any]:
        """Get component-specific performance metrics."""
        component_metrics = performance_monitor.get_component_metrics()
        
        components = {}
        for name, metrics in component_metrics.items():
            components[name] = {
                'average_execution_time_ms': metrics.average_execution_time_ms,
                'p95_execution_time_ms': metrics.p95_execution_time_ms,
                'error_rate': metrics.error_rate,
                'throughput_per_second': metrics.throughput_per_second,
                'total_executions': metrics.success_count + metrics.error_count,
                'success_count': metrics.success_count,
                'error_count': metrics.error_count,
                'last_execution': metrics.last_execution.isoformat() if metrics.last_execution else None,
                'status': self._get_component_status(metrics)
            }
        
        return {
            'components': components,
            'total_components': len(components),
            'healthy_components': len([c for c in components.values() if c['status'] == 'healthy']),
            'warning_components': len([c for c in components.values() if c['status'] == 'warning']),
            'critical_components': len([c for c in components.values() if c['status'] == 'critical'])
        }
    
    async def _get_websocket_status(self) -> Dict[str, Any]:
        """Get WebSocket connection status."""
        try:
            # Import here to avoid circular import
            from src.websocket.exchange_websocket_manager import exchange_websocket_manager
            websocket_stats = exchange_websocket_manager.get_performance_stats()

            return {
                'connected_exchanges': websocket_stats.get('connected_exchanges', []),
                'total_exchanges': websocket_stats.get('total_exchanges', 0),
                'connection_health': len(websocket_stats.get('connected_exchanges', [])) / max(websocket_stats.get('total_exchanges', 1), 1),
                'total_messages': websocket_stats.get('total_messages', 0),
                'subscribed_symbols': websocket_stats.get('subscribed_symbols', 0),
                'average_latency_ms': websocket_stats.get('average_latency_ms', 0),
                'data_freshness': websocket_stats.get('data_freshness', {}),
                'message_counts': websocket_stats.get('message_counts', {}),
                'error_counts': websocket_stats.get('error_counts', {}),
                'status': 'healthy' if len(websocket_stats.get('connected_exchanges', [])) > 0 else 'critical'
            }
        except Exception as e:
            logger.error(f"Error getting WebSocket status: {e}")
            return {
                'connected_exchanges': [],
                'total_exchanges': 0,
                'connection_health': 0,
                'total_messages': 0,
                'subscribed_symbols': 0,
                'average_latency_ms': 0,
                'data_freshness': {},
                'message_counts': {},
                'error_counts': {},
                'status': 'unknown'
            }
    
    async def _get_risk_management_status(self) -> Dict[str, Any]:
        """Get risk management status."""
        try:
            risk_stats = risk_manager.get_risk_statistics()
            
            circuit_breakers = risk_stats.get('circuit_breakers', {})
            open_breakers = [name for name, cb in circuit_breakers.items() if cb.get('is_open', False)]
            
            return {
                'daily_volume': risk_stats.get('daily_volume', 0),
                'max_daily_volume': risk_stats.get('limits', {}).get('max_daily_volume_usd', 0),
                'volume_utilization': (risk_stats.get('daily_volume', 0) / 
                                     max(risk_stats.get('limits', {}).get('max_daily_volume_usd', 1), 1)),
                'current_exposure': risk_stats.get('current_exposure', {}),
                'circuit_breakers': circuit_breakers,
                'open_circuit_breakers': open_breakers,
                'recent_trades': risk_stats.get('recent_trades', 0),
                'risk_limits': risk_stats.get('limits', {}),
                'status': 'critical' if open_breakers else 'healthy'
            }
        except Exception as e:
            logger.error(f"Error getting risk management status: {e}")
            return {
                'daily_volume': 0,
                'max_daily_volume': 0,
                'volume_utilization': 0,
                'current_exposure': {},
                'circuit_breakers': {},
                'open_circuit_breakers': [],
                'recent_trades': 0,
                'risk_limits': {},
                'status': 'unknown'
            }
    
    async def _get_optimization_status(self) -> Dict[str, Any]:
        """Get optimization status."""
        try:
            optimization_summary = performance_optimizer.get_optimization_summary()
            
            return {
                'optimization_level': optimization_summary.get('optimization_level', 'unknown'),
                'is_optimizing': optimization_summary.get('is_optimizing', False),
                'total_optimizations': optimization_summary.get('total_optimizations_applied', 0),
                'recent_optimizations': optimization_summary.get('recent_optimizations', 0),
                'average_improvement': optimization_summary.get('average_improvement_percent', 0),
                'last_optimization': optimization_summary.get('last_optimization_time', None),
                'current_parameters': optimization_summary.get('current_parameters', {}),
                'recent_recommendations': optimization_summary.get('recent_recommendations', []),
                'status': 'active' if optimization_summary.get('is_optimizing', False) else 'inactive'
            }
        except Exception as e:
            logger.error(f"Error getting optimization status: {e}")
            return {
                'optimization_level': 'unknown',
                'is_optimizing': False,
                'total_optimizations': 0,
                'recent_optimizations': 0,
                'average_improvement': 0,
                'last_optimization': None,
                'current_parameters': {},
                'recent_recommendations': [],
                'status': 'unknown'
            }
    
    async def _get_alerts_and_warnings(self) -> Dict[str, Any]:
        """Get current alerts and warnings."""
        alerts = []
        warnings = []
        
        # Check latency alerts
        current_metrics = performance_monitor.get_current_metrics()
        if current_metrics:
            if current_metrics.latency_ms > 200:
                alerts.append({
                    'type': 'critical_latency',
                    'message': f'Critical latency: {current_metrics.latency_ms:.1f}ms',
                    'timestamp': datetime.now().isoformat(),
                    'severity': 'critical'
                })
            elif current_metrics.latency_ms > 100:
                warnings.append({
                    'type': 'high_latency',
                    'message': f'High latency: {current_metrics.latency_ms:.1f}ms',
                    'timestamp': datetime.now().isoformat(),
                    'severity': 'warning'
                })
            
            # Check CPU alerts
            if current_metrics.cpu_percent > 90:
                alerts.append({
                    'type': 'critical_cpu',
                    'message': f'Critical CPU usage: {current_metrics.cpu_percent:.1f}%',
                    'timestamp': datetime.now().isoformat(),
                    'severity': 'critical'
                })
            elif current_metrics.cpu_percent > 80:
                warnings.append({
                    'type': 'high_cpu',
                    'message': f'High CPU usage: {current_metrics.cpu_percent:.1f}%',
                    'timestamp': datetime.now().isoformat(),
                    'severity': 'warning'
                })
            
            # Check error rate alerts
            if current_metrics.error_rate > 10:
                alerts.append({
                    'type': 'critical_errors',
                    'message': f'Critical error rate: {current_metrics.error_rate:.1f}%',
                    'timestamp': datetime.now().isoformat(),
                    'severity': 'critical'
                })
            elif current_metrics.error_rate > 5:
                warnings.append({
                    'type': 'high_errors',
                    'message': f'High error rate: {current_metrics.error_rate:.1f}%',
                    'timestamp': datetime.now().isoformat(),
                    'severity': 'warning'
                })
        
        # Check WebSocket connection alerts
        try:
            from src.websocket.exchange_websocket_manager import exchange_websocket_manager
            websocket_stats = exchange_websocket_manager.get_performance_stats()
            connected = len(websocket_stats.get('connected_exchanges', []))
            total = websocket_stats.get('total_exchanges', 0)

            if connected == 0:
                alerts.append({
                    'type': 'no_connections',
                    'message': 'No WebSocket connections active',
                    'timestamp': datetime.now().isoformat(),
                    'severity': 'critical'
                })
            elif connected < total:
                warnings.append({
                    'type': 'partial_connections',
                    'message': f'Only {connected}/{total} exchanges connected',
                    'timestamp': datetime.now().isoformat(),
                    'severity': 'warning'
                })
        except Exception:
            pass
        
        return {
            'alerts': alerts,
            'warnings': warnings,
            'total_alerts': len(alerts),
            'total_warnings': len(warnings),
            'overall_status': 'critical' if alerts else ('warning' if warnings else 'healthy')
        }
    
    def _calculate_trend(self, values: List[float]) -> str:
        """Calculate trend direction from a list of values."""
        if len(values) < 2:
            return 'stable'
        
        # Compare first and last quarters
        quarter_size = len(values) // 4
        if quarter_size == 0:
            return 'stable'
        
        first_quarter_avg = sum(values[:quarter_size]) / quarter_size
        last_quarter_avg = sum(values[-quarter_size:]) / quarter_size
        
        change_percent = ((last_quarter_avg - first_quarter_avg) / first_quarter_avg) * 100
        
        if change_percent > 10:
            return 'increasing'
        elif change_percent < -10:
            return 'decreasing'
        else:
            return 'stable'
    
    def _get_component_status(self, metrics) -> str:
        """Determine component status based on metrics."""
        if metrics.error_rate > 10:
            return 'critical'
        elif metrics.error_rate > 5 or metrics.average_execution_time_ms > 100:
            return 'warning'
        else:
            return 'healthy'
    
    def get_dashboard_data(self) -> Dict[str, Any]:
        """Get current dashboard data."""
        return self.dashboard_data.copy()
    
    def get_dashboard_json(self) -> str:
        """Get dashboard data as JSON string."""
        return json.dumps(self.dashboard_data, indent=2, default=str)
    
    def get_summary_stats(self) -> Dict[str, Any]:
        """Get summary statistics for the dashboard."""
        if not self.dashboard_data:
            return {'status': 'no_data'}
        
        system_overview = self.dashboard_data.get('system_overview', {})
        latency_metrics = self.dashboard_data.get('latency_metrics', {})
        alerts = self.dashboard_data.get('alerts_and_warnings', {})
        
        return {
            'overall_status': alerts.get('overall_status', 'unknown'),
            'current_latency_ms': latency_metrics.get('current_latency_ms', 0),
            'cpu_percent': system_overview.get('cpu_percent', 0),
            'memory_mb': system_overview.get('memory_mb', 0),
            'messages_per_second': system_overview.get('messages_per_second', 0),
            'error_rate': system_overview.get('error_rate', 0),
            'total_alerts': alerts.get('total_alerts', 0),
            'total_warnings': alerts.get('total_warnings', 0),
            'last_update': self.last_update_time.isoformat() if self.last_update_time else None
        }


# Global instance
performance_dashboard = PerformanceDashboard()
