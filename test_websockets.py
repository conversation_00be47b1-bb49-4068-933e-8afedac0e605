#!/usr/bin/env python3
"""
Test script to verify WebSocket connections and subscriptions.
"""
import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.websocket.exchanges.mexc_websocket import create_mexc_websocket
from src.websocket.exchanges.gate_websocket import create_gate_websocket
from src.websocket.exchanges.lbank_websocket import create_lbank_websocket
from src.config.settings import config
from src.utils.logger import logger


class MockExchangeConfig:
    """Mock exchange config for testing."""
    def __init__(self):
        self.timeout = 30


async def test_websocket_connection(exchange_name: str, websocket_manager, test_symbol: str = "BTC/USDT"):
    """Test a single WebSocket connection."""
    print(f"\n🔍 Testing {exchange_name} WebSocket...")
    
    try:
        # Start the WebSocket connection
        print(f"  📡 Connecting to {exchange_name}...")
        success = await websocket_manager.start()
        
        if not success:
            print(f"  ❌ Failed to connect to {exchange_name}")
            return False
        
        print(f"  ✅ Connected to {exchange_name}")
        
        # Test subscription
        print(f"  📊 Subscribing to {test_symbol} ticker...")
        sub_success = await websocket_manager.subscribe_ticker(test_symbol)
        
        if sub_success:
            print(f"  ✅ Successfully subscribed to {test_symbol}")
        else:
            print(f"  ⚠️ Subscription may have failed for {test_symbol}")
        
        # Wait a bit to see if we receive data
        print(f"  ⏳ Waiting 10 seconds for data...")
        await asyncio.sleep(10)
        
        # Check connection status
        if websocket_manager.state.name == "CONNECTED":
            print(f"  ✅ {exchange_name} connection is stable")
            return True
        else:
            print(f"  ❌ {exchange_name} connection lost: {websocket_manager.state.name}")
            return False
            
    except Exception as e:
        print(f"  ❌ Error testing {exchange_name}: {e}")
        return False
    finally:
        # Clean up
        try:
            await websocket_manager.stop()
            print(f"  🛑 {exchange_name} connection stopped")
        except Exception as e:
            print(f"  ⚠️ Error stopping {exchange_name}: {e}")


async def test_all_websockets():
    """Test all WebSocket connections."""
    print("🚀 WebSocket Connection Test")
    print("=" * 50)
    
    mock_config = MockExchangeConfig()
    results = {}
    
    # Test MEXC
    try:
        mexc_ws = create_mexc_websocket(mock_config)
        results['MEXC'] = await test_websocket_connection('MEXC', mexc_ws)
    except Exception as e:
        print(f"❌ MEXC test failed: {e}")
        results['MEXC'] = False
    
    # Test Gate.io
    try:
        gate_ws = create_gate_websocket(mock_config)
        results['Gate.io'] = await test_websocket_connection('Gate.io', gate_ws)
    except Exception as e:
        print(f"❌ Gate.io test failed: {e}")
        results['Gate.io'] = False
    
    # Test LBank
    try:
        lbank_ws = create_lbank_websocket(mock_config)
        results['LBank'] = await test_websocket_connection('LBank', lbank_ws)
    except Exception as e:
        print(f"❌ LBank test failed: {e}")
        results['LBank'] = False
    
    return results


async def main():
    """Main test function."""
    try:
        results = await test_all_websockets()
        
        print("\n📊 Test Results Summary:")
        print("=" * 30)
        
        success_count = 0
        for exchange, success in results.items():
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"  {exchange}: {status}")
            if success:
                success_count += 1
        
        print(f"\n🎯 Overall: {success_count}/{len(results)} exchanges working")
        
        if success_count == len(results):
            print("🎉 All WebSocket connections are working!")
            return 0
        elif success_count > 0:
            print("⚠️ Some WebSocket connections are working.")
            return 0
        else:
            print("💥 No WebSocket connections are working!")
            return 1
            
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
        return 1
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
