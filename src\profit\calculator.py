"""
Advanced profit calculation engine with comprehensive fee analysis.
"""
from dataclasses import dataclass
from typing import Dict, Op<PERSON>, Tuple, List
from decimal import Decimal, ROUND_HALF_UP
from datetime import datetime

from src.config.settings import config
from src.websocket.data_normalizer import NormalizedTickerData
from src.utils.logger import logger


@dataclass
class FeeStructure:
    """Fee structure for an exchange."""
    maker_fee: float = 0.001  # 0.1% default
    taker_fee: float = 0.001  # 0.1% default
    withdrawal_fee: float = 0.0  # Fixed withdrawal fee
    withdrawal_fee_percent: float = 0.0  # Percentage withdrawal fee
    minimum_trade: float = 10.0  # Minimum trade amount in USD
    network_fee: float = 0.0  # Blockchain network fee


@dataclass
class SlippageEstimate:
    """Slippage estimation based on orderbook depth."""
    buy_slippage: float = 0.0  # Expected slippage when buying
    sell_slippage: float = 0.0  # Expected slippage when selling
    confidence: float = 0.0  # Confidence level (0-1)
    orderbook_depth: float = 0.0  # Available liquidity


@dataclass
class ProfitCalculation:
    """Comprehensive profit calculation result."""
    # Basic information
    symbol: str
    buy_exchange: str
    sell_exchange: str
    buy_price: float
    sell_price: float
    trade_amount: float  # Amount in base currency
    
    # Fee breakdown
    buy_trading_fee: float
    sell_trading_fee: float
    withdrawal_fee: float
    network_fee: float
    total_fees: float
    
    # Slippage estimates
    buy_slippage: float
    sell_slippage: float
    total_slippage: float
    
    # Profit calculations
    gross_profit: float
    net_profit: float
    profit_percent: float
    roi_percent: float  # Return on investment
    
    # Risk metrics
    risk_score: float  # 0-1, higher is riskier
    confidence: float  # 0-1, confidence in calculation
    
    # Metadata
    calculation_time: datetime
    is_profitable: bool
    
    def to_dict(self) -> Dict:
        """Convert to dictionary for serialization."""
        return {
            'symbol': self.symbol,
            'buy_exchange': self.buy_exchange,
            'sell_exchange': self.sell_exchange,
            'buy_price': self.buy_price,
            'sell_price': self.sell_price,
            'trade_amount': self.trade_amount,
            'buy_trading_fee': self.buy_trading_fee,
            'sell_trading_fee': self.sell_trading_fee,
            'withdrawal_fee': self.withdrawal_fee,
            'network_fee': self.network_fee,
            'total_fees': self.total_fees,
            'buy_slippage': self.buy_slippage,
            'sell_slippage': self.sell_slippage,
            'total_slippage': self.total_slippage,
            'gross_profit': self.gross_profit,
            'net_profit': self.net_profit,
            'profit_percent': self.profit_percent,
            'roi_percent': self.roi_percent,
            'risk_score': self.risk_score,
            'confidence': self.confidence,
            'calculation_time': self.calculation_time.isoformat(),
            'is_profitable': self.is_profitable
        }


class ProfitCalculator:
    """Advanced profit calculator with comprehensive fee analysis."""
    
    def __init__(self):
        self.fee_structures = self._load_fee_structures()
        self.slippage_models = self._initialize_slippage_models()
        
    def _load_fee_structures(self) -> Dict[str, FeeStructure]:
        """Load fee structures for all exchanges."""
        fee_structures = {}
        
        # Load from configuration
        exchanges_config = config.exchanges
        
        for exchange_name, exchange_config in exchanges_config.items():
            fee_structures[exchange_name] = FeeStructure(
                maker_fee=exchange_config.commission_rate,
                taker_fee=exchange_config.commission_rate,
                withdrawal_fee=self._get_withdrawal_fee(exchange_name),
                withdrawal_fee_percent=self._get_withdrawal_fee_percent(exchange_name),
                minimum_trade=self._get_minimum_trade(exchange_name),
                network_fee=self._get_network_fee(exchange_name)
            )
        
        return fee_structures
    
    def _get_withdrawal_fee(self, exchange: str) -> float:
        """Get fixed withdrawal fee for exchange."""
        # These would typically come from configuration or API
        withdrawal_fees = {
            'gate': 1.0,    # $1 USDT withdrawal fee
            'mexc': 0.8,    # $0.8 USDT withdrawal fee
            'lbank': 1.5    # $1.5 USDT withdrawal fee
        }
        return withdrawal_fees.get(exchange, 1.0)
    
    def _get_withdrawal_fee_percent(self, exchange: str) -> float:
        """Get percentage withdrawal fee for exchange."""
        # Most exchanges use fixed fees for USDT
        return 0.0
    
    def _get_minimum_trade(self, exchange: str) -> float:
        """Get minimum trade amount for exchange."""
        minimum_trades = {
            'gate': 5.0,
            'mexc': 10.0,
            'lbank': 10.0
        }
        return minimum_trades.get(exchange, 10.0)
    
    def _get_network_fee(self, exchange: str) -> float:
        """Get network fee for transfers."""
        # Network fees for USDT transfers (varies by network)
        network_fees = {
            'gate': 0.1,    # TRC20 network
            'mexc': 0.1,    # TRC20 network
            'lbank': 0.1    # TRC20 network
        }
        return network_fees.get(exchange, 0.1)
    
    def _initialize_slippage_models(self) -> Dict[str, Dict]:
        """Initialize slippage estimation models."""
        # Simple slippage models based on spread and volume
        return {
            'gate': {'base_slippage': 0.001, 'volume_factor': 0.0001},
            'mexc': {'base_slippage': 0.0015, 'volume_factor': 0.00015},
            'lbank': {'base_slippage': 0.002, 'volume_factor': 0.0002}
        }
    
    def calculate_profit(
        self,
        buy_ticker: NormalizedTickerData,
        sell_ticker: NormalizedTickerData,
        trade_amount_usd: float = 100.0
    ) -> ProfitCalculation:
        """
        Calculate comprehensive profit for an arbitrage opportunity.
        
        Args:
            buy_ticker: Ticker data for buying exchange
            sell_ticker: Ticker data for selling exchange
            trade_amount_usd: Trade amount in USD
        
        Returns:
            ProfitCalculation with detailed breakdown
        """
        try:
            # Validate inputs
            if not self._validate_tickers(buy_ticker, sell_ticker):
                raise ValueError("Invalid ticker data")
            
            # Get fee structures
            buy_fees = self.fee_structures.get(buy_ticker.exchange)
            sell_fees = self.fee_structures.get(sell_ticker.exchange)
            
            if not buy_fees or not sell_fees:
                raise ValueError(f"Fee structure not found for exchanges")
            
            # Calculate trade amounts
            base_amount = trade_amount_usd / buy_ticker.ask  # Amount of base currency
            
            # Calculate trading fees
            buy_trading_fee = trade_amount_usd * buy_fees.taker_fee
            sell_trading_fee = (base_amount * sell_ticker.bid) * sell_fees.taker_fee
            
            # Calculate withdrawal and network fees
            withdrawal_fee = buy_fees.withdrawal_fee
            network_fee = buy_fees.network_fee
            
            # Estimate slippage
            buy_slippage_est = self._estimate_slippage(
                buy_ticker, trade_amount_usd, 'buy'
            )
            sell_slippage_est = self._estimate_slippage(
                sell_ticker, base_amount * sell_ticker.bid, 'sell'
            )
            
            # Calculate slippage costs
            buy_slippage_cost = trade_amount_usd * buy_slippage_est.buy_slippage
            sell_slippage_cost = (base_amount * sell_ticker.bid) * sell_slippage_est.sell_slippage
            
            # Total costs
            total_fees = buy_trading_fee + sell_trading_fee + withdrawal_fee + network_fee
            total_slippage = buy_slippage_cost + sell_slippage_cost
            
            # Profit calculations
            gross_revenue = base_amount * sell_ticker.bid
            gross_cost = trade_amount_usd
            gross_profit = gross_revenue - gross_cost
            
            net_profit = gross_profit - total_fees - total_slippage
            profit_percent = (net_profit / gross_cost) * 100 if gross_cost > 0 else 0
            roi_percent = (net_profit / trade_amount_usd) * 100 if trade_amount_usd > 0 else 0
            
            # Risk assessment
            risk_score = self._calculate_risk_score(
                buy_ticker, sell_ticker, buy_slippage_est, sell_slippage_est
            )
            
            # Confidence calculation
            confidence = min(buy_slippage_est.confidence, sell_slippage_est.confidence)
            
            return ProfitCalculation(
                symbol=buy_ticker.symbol,
                buy_exchange=buy_ticker.exchange,
                sell_exchange=sell_ticker.exchange,
                buy_price=buy_ticker.ask,
                sell_price=sell_ticker.bid,
                trade_amount=base_amount,
                buy_trading_fee=buy_trading_fee,
                sell_trading_fee=sell_trading_fee,
                withdrawal_fee=withdrawal_fee,
                network_fee=network_fee,
                total_fees=total_fees,
                buy_slippage=buy_slippage_cost,
                sell_slippage=sell_slippage_cost,
                total_slippage=total_slippage,
                gross_profit=gross_profit,
                net_profit=net_profit,
                profit_percent=profit_percent,
                roi_percent=roi_percent,
                risk_score=risk_score,
                confidence=confidence,
                calculation_time=datetime.now(),
                is_profitable=net_profit > 0
            )
            
        except Exception as e:
            logger.error(f"Error calculating profit: {e}")
            raise
    
    def _validate_tickers(self, buy_ticker: NormalizedTickerData, 
                         sell_ticker: NormalizedTickerData) -> bool:
        """Validate ticker data for profit calculation."""
        if not buy_ticker or not sell_ticker:
            return False
        
        if buy_ticker.symbol != sell_ticker.symbol:
            return False
        
        if buy_ticker.ask <= 0 or sell_ticker.bid <= 0:
            return False
        
        if buy_ticker.exchange == sell_ticker.exchange:
            return False
        
        # Check data freshness (within last 60 seconds)
        now = datetime.now()
        if ((now - buy_ticker.timestamp).total_seconds() > 60 or
            (now - sell_ticker.timestamp).total_seconds() > 60):
            return False
        
        return True
    
    def _estimate_slippage(self, ticker: NormalizedTickerData, 
                          trade_amount: float, side: str) -> SlippageEstimate:
        """Estimate slippage based on spread and volume."""
        try:
            exchange = ticker.exchange
            model = self.slippage_models.get(exchange, {})
            
            base_slippage = model.get('base_slippage', 0.002)
            volume_factor = model.get('volume_factor', 0.0002)
            
            # Calculate slippage based on spread and volume
            spread_percent = ticker.spread_percent / 100
            volume_impact = volume_factor * (trade_amount / max(ticker.volume * ticker.last, 1))
            
            if side == 'buy':
                slippage = base_slippage + spread_percent * 0.5 + volume_impact
                return SlippageEstimate(
                    buy_slippage=slippage,
                    sell_slippage=0,
                    confidence=0.8,  # Medium confidence
                    orderbook_depth=ticker.volume * ticker.last
                )
            else:
                slippage = base_slippage + spread_percent * 0.5 + volume_impact
                return SlippageEstimate(
                    buy_slippage=0,
                    sell_slippage=slippage,
                    confidence=0.8,
                    orderbook_depth=ticker.volume * ticker.last
                )
                
        except Exception as e:
            logger.error(f"Error estimating slippage: {e}")
            # Return conservative estimate
            return SlippageEstimate(
                buy_slippage=0.005 if side == 'buy' else 0,
                sell_slippage=0.005 if side == 'sell' else 0,
                confidence=0.5,
                orderbook_depth=0
            )
    
    def _calculate_risk_score(self, buy_ticker: NormalizedTickerData,
                             sell_ticker: NormalizedTickerData,
                             buy_slippage: SlippageEstimate,
                             sell_slippage: SlippageEstimate) -> float:
        """Calculate risk score for the arbitrage opportunity."""
        try:
            risk_factors = []
            
            # Spread risk (higher spread = higher risk)
            buy_spread_risk = min(buy_ticker.spread_percent / 2.0, 1.0)
            sell_spread_risk = min(sell_ticker.spread_percent / 2.0, 1.0)
            risk_factors.append((buy_spread_risk + sell_spread_risk) / 2)
            
            # Volume risk (lower volume = higher risk)
            min_volume = min(buy_ticker.volume, sell_ticker.volume)
            volume_risk = max(0, 1.0 - (min_volume / 10000))  # Normalize to 10k volume
            risk_factors.append(volume_risk)
            
            # Slippage risk
            total_slippage = buy_slippage.buy_slippage + sell_slippage.sell_slippage
            slippage_risk = min(total_slippage * 100, 1.0)  # Convert to 0-1 scale
            risk_factors.append(slippage_risk)
            
            # Data freshness risk
            now = datetime.now()
            buy_age = (now - buy_ticker.timestamp).total_seconds()
            sell_age = (now - sell_ticker.timestamp).total_seconds()
            max_age = max(buy_age, sell_age)
            freshness_risk = min(max_age / 60.0, 1.0)  # 60 seconds = max risk
            risk_factors.append(freshness_risk)
            
            # Calculate weighted average
            weights = [0.3, 0.25, 0.25, 0.2]  # Spread, volume, slippage, freshness
            risk_score = sum(risk * weight for risk, weight in zip(risk_factors, weights))
            
            return min(max(risk_score, 0.0), 1.0)  # Clamp to 0-1
            
        except Exception as e:
            logger.error(f"Error calculating risk score: {e}")
            return 0.8  # Conservative high risk


# Global instance
profit_calculator = ProfitCalculator()
