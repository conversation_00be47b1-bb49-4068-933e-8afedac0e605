"""
Mock exchange connector for testing and demonstration purposes.
"""
import asyncio
import random
from datetime import datetime
from typing import Optional

from src.database.models import PriceData
from src.utils.logger import logger


class MockExchangeConnector:
    """A mock exchange connector that generates fake price data."""

    def __init__(self, exchange_name: str, exchange_config: dict):
        self.name = exchange_name
        self.config = exchange_config
        self.is_connected = False
        self.last_error = None

    async def initialize(self):
        """Initialize the mock exchange."""
        logger.info(f"Initializing mock exchange: {self.name}")
        self.is_connected = True
        logger.info(f"Mock exchange {self.name} initialized successfully.")

    async def fetch_ticker(self, symbol: str) -> Optional[PriceData]:
        """Generate a fake ticker for the given symbol."""
        if not self.is_connected:
            return None

        try:
            # Simulate some price variation
            base_price = 100.0
            price = base_price + random.uniform(-2.0, 2.0)
            volume = random.uniform(1000, 5000)
            bid = price - random.uniform(0.01, 0.05)
            ask = price + random.uniform(0.01, 0.05)

            return PriceData(
                exchange=self.name,
                symbol=symbol,
                price=price,
                volume=volume,
                bid=bid,
                ask=ask,
                timestamp=datetime.now(),
            )
        except Exception as e:
            logger.error(f"Error in mock ticker for {symbol} from {self.name}: {e}")
            return None

    async def close(self):
        """Close the mock exchange connection."""
        self.is_connected = False
        logger.info(f"Closed connection to mock exchange: {self.name}")
