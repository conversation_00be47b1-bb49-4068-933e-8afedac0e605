"""
Performance optimizer with automatic tuning and optimization recommendations.
"""
import asyncio
import statistics
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

from src.monitoring.performance_monitor import performance_monitor, PerformanceMetrics
from src.utils.logger import logger


class OptimizationLevel(Enum):
    """Optimization levels."""
    CONSERVATIVE = "conservative"
    MODERATE = "moderate"
    AGGRESSIVE = "aggressive"


@dataclass
class OptimizationRecommendation:
    """Optimization recommendation."""
    component: str
    issue: str
    recommendation: str
    impact: str  # low, medium, high
    effort: str  # low, medium, high
    estimated_improvement: str
    priority: int  # 1-10, higher is more important
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'component': self.component,
            'issue': self.issue,
            'recommendation': self.recommendation,
            'impact': self.impact,
            'effort': self.effort,
            'estimated_improvement': self.estimated_improvement,
            'priority': self.priority
        }


@dataclass
class OptimizationResult:
    """Result of an optimization action."""
    component: str
    action: str
    before_metrics: Dict[str, float]
    after_metrics: Dict[str, float]
    improvement_percent: float
    success: bool
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'component': self.component,
            'action': self.action,
            'before_metrics': self.before_metrics,
            'after_metrics': self.after_metrics,
            'improvement_percent': self.improvement_percent,
            'success': self.success,
            'timestamp': self.timestamp.isoformat()
        }


class PerformanceOptimizer:
    """
    Automatic performance optimizer that analyzes metrics and provides
    optimization recommendations and automatic tuning.
    """
    
    def __init__(self, optimization_level: OptimizationLevel = OptimizationLevel.MODERATE):
        self.optimization_level = optimization_level
        self.is_optimizing = False
        
        # Optimization history
        self.recommendations: List[OptimizationRecommendation] = []
        self.optimization_results: List[OptimizationResult] = []
        
        # Tunable parameters
        self.tunable_parameters = {
            'websocket_ping_interval': {'min': 10, 'max': 60, 'current': 30},
            'websocket_reconnect_delay': {'min': 0.5, 'max': 5.0, 'current': 1.0},
            'risk_assessment_timeout': {'min': 1, 'max': 10, 'current': 5},
            'profit_calculation_cache_size': {'min': 100, 'max': 10000, 'current': 1000},
            'message_batch_size': {'min': 1, 'max': 100, 'current': 10},
            'processing_thread_pool_size': {'min': 1, 'max': 16, 'current': 4}
        }
        
        # Performance baselines
        self.baseline_metrics: Optional[Dict[str, float]] = None
        self.last_optimization_time = datetime.now()
        
    async def start_optimization(self, interval_minutes: int = 30):
        """Start automatic optimization process."""
        if self.is_optimizing:
            logger.warning("Performance optimization already running")
            return
        
        self.is_optimizing = True
        logger.info(f"Performance optimization started with {interval_minutes}min interval")
        
        # Start optimization loop
        asyncio.create_task(self._optimization_loop(interval_minutes))
    
    async def stop_optimization(self):
        """Stop automatic optimization."""
        self.is_optimizing = False
        logger.info("Performance optimization stopped")
    
    async def _optimization_loop(self, interval_minutes: int):
        """Main optimization loop."""
        while self.is_optimizing:
            try:
                await self._run_optimization_cycle()
                await asyncio.sleep(interval_minutes * 60)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in optimization loop: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retrying
    
    async def _run_optimization_cycle(self):
        """Run a complete optimization cycle."""
        logger.info("Starting optimization cycle")
        
        try:
            # Collect current metrics
            current_metrics = await self._collect_optimization_metrics()
            
            # Update baseline if needed
            if self.baseline_metrics is None:
                self.baseline_metrics = current_metrics
                logger.info("Established performance baseline")
                return
            
            # Analyze performance
            recommendations = await self._analyze_performance(current_metrics)
            
            # Apply optimizations based on level
            if recommendations:
                await self._apply_optimizations(recommendations)
            
            # Update last optimization time
            self.last_optimization_time = datetime.now()
            
        except Exception as e:
            logger.error(f"Error in optimization cycle: {e}")
    
    async def _collect_optimization_metrics(self) -> Dict[str, float]:
        """Collect metrics for optimization analysis."""
        current_metrics = performance_monitor.get_current_metrics()
        component_metrics = performance_monitor.get_component_metrics()
        
        metrics = {}
        
        if current_metrics:
            metrics.update({
                'latency_ms': current_metrics.latency_ms,
                'cpu_percent': current_metrics.cpu_percent,
                'memory_mb': current_metrics.memory_mb,
                'messages_per_second': current_metrics.messages_per_second,
                'error_rate': current_metrics.error_rate
            })
        
        # Add component-specific metrics
        for name, component in component_metrics.items():
            metrics[f'{name}_avg_execution_ms'] = component.average_execution_time_ms
            metrics[f'{name}_p95_execution_ms'] = component.p95_execution_time_ms
            metrics[f'{name}_error_rate'] = component.error_rate
            metrics[f'{name}_throughput'] = component.throughput_per_second
        
        return metrics
    
    async def _analyze_performance(self, current_metrics: Dict[str, float]) -> List[OptimizationRecommendation]:
        """Analyze performance and generate recommendations."""
        recommendations = []
        
        # Latency analysis
        if current_metrics.get('latency_ms', 0) > 100:
            recommendations.extend(self._analyze_latency_issues(current_metrics))
        
        # CPU analysis
        if current_metrics.get('cpu_percent', 0) > 70:
            recommendations.extend(self._analyze_cpu_issues(current_metrics))
        
        # Memory analysis
        if current_metrics.get('memory_mb', 0) > 500:
            recommendations.extend(self._analyze_memory_issues(current_metrics))
        
        # Error rate analysis
        if current_metrics.get('error_rate', 0) > 2:
            recommendations.extend(self._analyze_error_issues(current_metrics))
        
        # Component-specific analysis
        recommendations.extend(self._analyze_component_performance(current_metrics))
        
        # Sort by priority
        recommendations.sort(key=lambda x: x.priority, reverse=True)
        
        # Store recommendations
        self.recommendations.extend(recommendations)
        
        # Keep only recent recommendations
        cutoff_time = datetime.now() - timedelta(hours=24)
        self.recommendations = [
            rec for rec in self.recommendations 
            if hasattr(rec, 'timestamp') and rec.timestamp > cutoff_time
        ]
        
        return recommendations
    
    def _analyze_latency_issues(self, metrics: Dict[str, float]) -> List[OptimizationRecommendation]:
        """Analyze latency-related performance issues."""
        recommendations = []
        latency = metrics.get('latency_ms', 0)
        
        if latency > 200:
            recommendations.append(OptimizationRecommendation(
                component="websocket",
                issue=f"High latency detected: {latency:.1f}ms",
                recommendation="Reduce WebSocket ping interval and optimize message processing",
                impact="high",
                effort="medium",
                estimated_improvement="30-50% latency reduction",
                priority=9
            ))
        elif latency > 100:
            recommendations.append(OptimizationRecommendation(
                component="processing",
                issue=f"Moderate latency: {latency:.1f}ms",
                recommendation="Optimize data processing pipeline and increase batch sizes",
                impact="medium",
                effort="low",
                estimated_improvement="15-25% latency reduction",
                priority=6
            ))
        
        return recommendations
    
    def _analyze_cpu_issues(self, metrics: Dict[str, float]) -> List[OptimizationRecommendation]:
        """Analyze CPU-related performance issues."""
        recommendations = []
        cpu_percent = metrics.get('cpu_percent', 0)
        
        if cpu_percent > 80:
            recommendations.append(OptimizationRecommendation(
                component="threading",
                issue=f"High CPU usage: {cpu_percent:.1f}%",
                recommendation="Increase thread pool size and optimize async operations",
                impact="high",
                effort="medium",
                estimated_improvement="20-40% CPU reduction",
                priority=8
            ))
        elif cpu_percent > 70:
            recommendations.append(OptimizationRecommendation(
                component="algorithms",
                issue=f"Elevated CPU usage: {cpu_percent:.1f}%",
                recommendation="Optimize calculation algorithms and add caching",
                impact="medium",
                effort="high",
                estimated_improvement="10-20% CPU reduction",
                priority=5
            ))
        
        return recommendations
    
    def _analyze_memory_issues(self, metrics: Dict[str, float]) -> List[OptimizationRecommendation]:
        """Analyze memory-related performance issues."""
        recommendations = []
        memory_mb = metrics.get('memory_mb', 0)
        
        if memory_mb > 1000:
            recommendations.append(OptimizationRecommendation(
                component="caching",
                issue=f"High memory usage: {memory_mb:.1f}MB",
                recommendation="Implement memory-efficient caching and data cleanup",
                impact="medium",
                effort="medium",
                estimated_improvement="30-50% memory reduction",
                priority=7
            ))
        
        return recommendations
    
    def _analyze_error_issues(self, metrics: Dict[str, float]) -> List[OptimizationRecommendation]:
        """Analyze error-related performance issues."""
        recommendations = []
        error_rate = metrics.get('error_rate', 0)
        
        if error_rate > 5:
            recommendations.append(OptimizationRecommendation(
                component="error_handling",
                issue=f"High error rate: {error_rate:.1f}%",
                recommendation="Improve error handling and add circuit breakers",
                impact="high",
                effort="medium",
                estimated_improvement="50-80% error reduction",
                priority=10
            ))
        
        return recommendations
    
    def _analyze_component_performance(self, metrics: Dict[str, float]) -> List[OptimizationRecommendation]:
        """Analyze component-specific performance issues."""
        recommendations = []
        
        # Check for slow components
        for key, value in metrics.items():
            if key.endswith('_avg_execution_ms') and value > 50:
                component_name = key.replace('_avg_execution_ms', '')
                recommendations.append(OptimizationRecommendation(
                    component=component_name,
                    issue=f"Slow component execution: {value:.1f}ms average",
                    recommendation=f"Optimize {component_name} processing logic",
                    impact="medium",
                    effort="medium",
                    estimated_improvement="20-40% execution time reduction",
                    priority=6
                ))
        
        return recommendations
    
    async def _apply_optimizations(self, recommendations: List[OptimizationRecommendation]):
        """Apply optimizations based on recommendations and optimization level."""
        applied_count = 0
        max_optimizations = self._get_max_optimizations_per_cycle()
        
        for recommendation in recommendations[:max_optimizations]:
            if await self._should_apply_optimization(recommendation):
                try:
                    result = await self._apply_single_optimization(recommendation)
                    if result and result.success:
                        applied_count += 1
                        logger.info(f"Applied optimization: {recommendation.recommendation}")
                    
                except Exception as e:
                    logger.error(f"Failed to apply optimization: {e}")
        
        if applied_count > 0:
            logger.info(f"Applied {applied_count} optimizations")
    
    def _get_max_optimizations_per_cycle(self) -> int:
        """Get maximum optimizations to apply per cycle based on level."""
        if self.optimization_level == OptimizationLevel.CONSERVATIVE:
            return 1
        elif self.optimization_level == OptimizationLevel.MODERATE:
            return 2
        else:  # AGGRESSIVE
            return 3
    
    async def _should_apply_optimization(self, recommendation: OptimizationRecommendation) -> bool:
        """Determine if an optimization should be applied."""
        # Check optimization level compatibility
        if self.optimization_level == OptimizationLevel.CONSERVATIVE:
            return recommendation.effort == "low" and recommendation.priority >= 8
        elif self.optimization_level == OptimizationLevel.MODERATE:
            return recommendation.effort in ["low", "medium"] and recommendation.priority >= 6
        else:  # AGGRESSIVE
            return recommendation.priority >= 4
    
    async def _apply_single_optimization(self, recommendation: OptimizationRecommendation) -> Optional[OptimizationResult]:
        """Apply a single optimization."""
        before_metrics = await self._collect_optimization_metrics()
        
        # Apply optimization based on component
        success = False
        action = ""
        
        if recommendation.component == "websocket":
            success, action = await self._optimize_websocket(recommendation)
        elif recommendation.component == "processing":
            success, action = await self._optimize_processing(recommendation)
        elif recommendation.component == "threading":
            success, action = await self._optimize_threading(recommendation)
        elif recommendation.component == "caching":
            success, action = await self._optimize_caching(recommendation)
        
        if success:
            # Wait for changes to take effect
            await asyncio.sleep(10)
            
            # Collect after metrics
            after_metrics = await self._collect_optimization_metrics()
            
            # Calculate improvement
            improvement = self._calculate_improvement(before_metrics, after_metrics)
            
            result = OptimizationResult(
                component=recommendation.component,
                action=action,
                before_metrics=before_metrics,
                after_metrics=after_metrics,
                improvement_percent=improvement,
                success=True,
                timestamp=datetime.now()
            )
            
            self.optimization_results.append(result)
            return result
        
        return None
    
    async def _optimize_websocket(self, recommendation: OptimizationRecommendation) -> Tuple[bool, str]:
        """Optimize WebSocket parameters."""
        if "ping interval" in recommendation.recommendation.lower():
            # Reduce ping interval
            current = self.tunable_parameters['websocket_ping_interval']['current']
            new_value = max(current - 5, self.tunable_parameters['websocket_ping_interval']['min'])
            self.tunable_parameters['websocket_ping_interval']['current'] = new_value
            return True, f"Reduced WebSocket ping interval to {new_value}s"
        
        return False, ""
    
    async def _optimize_processing(self, recommendation: OptimizationRecommendation) -> Tuple[bool, str]:
        """Optimize processing parameters."""
        if "batch" in recommendation.recommendation.lower():
            # Increase batch size
            current = self.tunable_parameters['message_batch_size']['current']
            new_value = min(current + 5, self.tunable_parameters['message_batch_size']['max'])
            self.tunable_parameters['message_batch_size']['current'] = new_value
            return True, f"Increased message batch size to {new_value}"
        
        return False, ""
    
    async def _optimize_threading(self, recommendation: OptimizationRecommendation) -> Tuple[bool, str]:
        """Optimize threading parameters."""
        if "thread pool" in recommendation.recommendation.lower():
            # Increase thread pool size
            current = self.tunable_parameters['processing_thread_pool_size']['current']
            new_value = min(current + 2, self.tunable_parameters['processing_thread_pool_size']['max'])
            self.tunable_parameters['processing_thread_pool_size']['current'] = new_value
            return True, f"Increased thread pool size to {new_value}"
        
        return False, ""
    
    async def _optimize_caching(self, recommendation: OptimizationRecommendation) -> Tuple[bool, str]:
        """Optimize caching parameters."""
        if "cache" in recommendation.recommendation.lower():
            # Adjust cache size
            current = self.tunable_parameters['profit_calculation_cache_size']['current']
            new_value = min(current + 500, self.tunable_parameters['profit_calculation_cache_size']['max'])
            self.tunable_parameters['profit_calculation_cache_size']['current'] = new_value
            return True, f"Increased cache size to {new_value}"
        
        return False, ""
    
    def _calculate_improvement(self, before: Dict[str, float], after: Dict[str, float]) -> float:
        """Calculate overall improvement percentage."""
        improvements = []
        
        # Key metrics to compare
        key_metrics = ['latency_ms', 'cpu_percent', 'error_rate']
        
        for metric in key_metrics:
            if metric in before and metric in after:
                before_val = before[metric]
                after_val = after[metric]
                
                if before_val > 0:
                    # For metrics where lower is better
                    improvement = ((before_val - after_val) / before_val) * 100
                    improvements.append(improvement)
        
        return statistics.mean(improvements) if improvements else 0.0
    
    def get_optimization_summary(self) -> Dict[str, Any]:
        """Get optimization summary and statistics."""
        recent_results = [
            result for result in self.optimization_results
            if (datetime.now() - result.timestamp).total_seconds() < 86400  # Last 24 hours
        ]
        
        total_improvement = sum(result.improvement_percent for result in recent_results)
        avg_improvement = total_improvement / len(recent_results) if recent_results else 0
        
        return {
            'optimization_level': self.optimization_level.value,
            'is_optimizing': self.is_optimizing,
            'total_optimizations_applied': len(self.optimization_results),
            'recent_optimizations': len(recent_results),
            'average_improvement_percent': avg_improvement,
            'last_optimization_time': self.last_optimization_time.isoformat(),
            'current_parameters': dict(self.tunable_parameters),
            'recent_recommendations': [rec.to_dict() for rec in self.recommendations[-10:]],
            'recent_results': [result.to_dict() for result in recent_results[-5:]]
        }


# Global instance
performance_optimizer = PerformanceOptimizer()
