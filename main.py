"""
Main entry point for the cryptocurrency arbitrage detection bot.
"""
import asyncio
import signal
import sys
import platform
from datetime import datetime, timedelta

from src.config.settings import config
from src.database.models import db_manager
from src.exchanges.exchange_manager import exchange_manager
from src.arbitrage.engine import arbitrage_engine
from src.notifications.telegram_bot import telegram_notifier
from src.utils.logger import logger


class ArbitrageBot:
    """Main class for the arbitrage bot."""
    
    def __init__(self):
        self.is_running = False
        self.last_summary_time = datetime.now()
        self.last_cleanup_time = datetime.now()
        
    async def initialize(self):
        """Initialize all bot components."""
        logger.info("Initializing arbitrage bot...")
        
        # Initialize database
        await db_manager.initialize()
        logger.info("Database initialized")
        
        # Initialize exchange manager
        await exchange_manager.initialize()
        logger.info("Exchange manager initialized")
        
        # Initialize Telegram notifier
        await telegram_notifier.initialize()
        logger.info("Telegram notifier initialized")
        
        logger.info("Bot initialization complete")
    
    async def run(self):
        """Main bot loop."""
        self.is_running = True
        logger.info("Arbitrage bot started")
        
        if telegram_notifier.is_configured:
            await telegram_notifier.send_status_update("Bot started successfully")
        
        while self.is_running:
            try:
                # Analyze all symbols for arbitrage
                opportunities = await arbitrage_engine.analyze_all_symbols()
                
                # Process found opportunities
                if opportunities:
                    await self._process_opportunities(opportunities)
                
                # Perform periodic tasks
                await self._periodic_tasks()
                
                # Wait for next interval
                await asyncio.sleep(config.bot.monitoring_interval)
                
            except asyncio.CancelledError:
                logger.info("Bot loop cancelled")
                break
            except Exception as e:
                logger.error(f"An error occurred in the main loop: {e}")
                if telegram_notifier.is_configured:
                    await telegram_notifier.send_error_notification(str(e))
                await asyncio.sleep(60)  # Wait before retrying
    
    async def _process_opportunities(self, opportunities: dict):
        """Process and send notifications for arbitrage opportunities."""
        for symbol, signals in opportunities.items():
            for signal in signals:
                try:
                    # Insert signal into database
                    signal_id = await db_manager.insert_arbitrage_signal(signal)
                    signal.id = signal_id
                    
                    # Send notification
                    if telegram_notifier.is_configured:
                        await telegram_notifier.send_arbitrage_signal(signal)
                    
                except Exception as e:
                    logger.error(f"Error processing opportunity for {symbol}: {e}")
    
    async def _periodic_tasks(self):
        """Perform periodic tasks like sending summaries and cleaning up."""
        now = datetime.now()
        
        # Daily summary
        if (now - self.last_summary_time).days >= 1:
            await self._send_daily_summary()
            self.last_summary_time = now
        
        # Database cleanup
        if (now - self.last_cleanup_time).seconds >= config.database.backup_interval:
            await self._cleanup_database()
            self.last_cleanup_time = now
        
        # Reconnect failed exchanges
        await exchange_manager.reconnect_failed_exchanges()
    
    async def _send_daily_summary(self):
        """Generate and send daily summary."""
        if not telegram_notifier.is_configured:
            return
        
        try:
            logger.info("Generating daily summary...")
            summary_data = await arbitrage_engine.calculate_historical_performance(days=1)
            await telegram_notifier.send_daily_summary(summary_data)
            
        except Exception as e:
            logger.error(f"Error sending daily summary: {e}")
    
    async def _cleanup_database(self):
        """Clean up old data from the database."""
        try:
            logger.info("Performing database cleanup...")
            await db_manager.cleanup_old_data(config.database.cleanup_days)
            logger.info("Database cleanup complete")
            
        except Exception as e:
            logger.error(f"Error during database cleanup: {e}")
    
    async def shutdown(self):
        """Gracefully shut down the bot."""
        self.is_running = False
        logger.info("Shutting down arbitrage bot...")
        
        if telegram_notifier.is_configured:
            await telegram_notifier.send_status_update("Bot is shutting down")
        
        # Close exchange connections
        await exchange_manager.close_all()
        
        logger.info("Bot shutdown complete")


async def main():
    """Main function to run the bot."""
    bot = ArbitrageBot()

    try:
        await bot.initialize()
        await bot.run()

    except (KeyboardInterrupt, asyncio.CancelledError):
        logger.info("Shutdown signal received")

    except Exception as e:
        logger.critical(f"A critical error occurred: {e}")
        if telegram_notifier.is_configured:
            await telegram_notifier.send_error_notification(f"Critical error: {e}")

    finally:
        if bot.is_running:
            await bot.shutdown()

        # Wait for all tasks to complete with timeout
        tasks = [t for t in asyncio.all_tasks() if t is not asyncio.current_task()]
        if tasks:
            try:
                await asyncio.wait_for(
                    asyncio.gather(*tasks, return_exceptions=True),
                    timeout=10.0
                )
            except asyncio.TimeoutError:
                logger.warning("Some tasks did not complete within timeout, forcing shutdown")
                for task in tasks:
                    if not task.done():
                        task.cancel()
                # Wait a bit more for cancellation to complete
                try:
                    await asyncio.wait_for(
                        asyncio.gather(*tasks, return_exceptions=True),
                        timeout=2.0
                    )
                except asyncio.TimeoutError:
                    logger.warning("Some tasks could not be cancelled")


def setup_windows_asyncio():
    """Setup Windows-specific asyncio configuration to prevent socket errors."""
    if platform.system() == 'Windows':
        # Set the event loop policy to prevent ProactorEventLoop socket errors
        if sys.version_info >= (3, 8):
            try:
                # Use WindowsSelectorEventLoop instead of WindowsProactorEventLoop
                asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
                logger.info("Set Windows Selector Event Loop Policy")
            except AttributeError:
                # Fallback for older Python versions
                logger.warning("WindowsSelectorEventLoopPolicy not available, using default")

        # Additional Windows-specific settings
        try:
            # Increase the default timeout for socket operations
            import socket
            socket.setdefaulttimeout(30.0)
        except Exception as e:
            logger.warning(f"Could not set socket timeout: {e}")


if __name__ == "__main__":
    # Setup Windows-specific asyncio configuration
    setup_windows_asyncio()

    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)
