name: Run Unit Tests

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        python-version: [3.11, 3.12]

    steps:
      - uses: actions/checkout@v4

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install -r requirements-test.txt

      - name: Run tests
        run: |
          python -m pytest tests/ -v --tb=short

      - name: Run tests with coverage
        run: |
          python -m pytest tests/ --cov=src --cov-report=xml --cov-report=html

      # - name: Upload coverage to Codecov
      #   uses: codecov/codecov-action@v3
      #   with:
      #     file: ./coverage.xml
      #     flags: unittests
      #     name: codecov-umbrella
