#!/usr/bin/env python3
"""
Direct launcher for the enhanced arbitrage bot from src directory.
"""
import asyncio
import sys
from pathlib import Path

# Ensure we can import from current directory
sys.path.insert(0, str(Path(__file__).parent))

from main_enhanced import main

if __name__ == "__main__":
    print("🚀 Starting Enhanced Arbitrage Bot with WebSocket Real-time Processing")
    print("=" * 70)
    print("Features:")
    print("  ✅ Real-time WebSocket data from Gate.io, MEXC, LBank")
    print("  ✅ Advanced profit calculation with fees & slippage")
    print("  ✅ Comprehensive risk management")
    print("  ✅ Performance monitoring (<100ms latency)")
    print("  ✅ Automatic optimization")
    print("  ✅ Telegram notifications")
    print("=" * 70)
    
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 Bot stopped by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
