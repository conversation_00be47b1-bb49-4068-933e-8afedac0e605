# Arbitrage Bot Configuration
bot:
  name: "Crypto Arbitrage Bot"
  version: "1.0.0"
  monitoring_interval: 15 # seconds - increased to reduce API calls
  profit_threshold: 0.02 # minimum profit percentage
  max_signal_frequency: 300 # seconds between same pair signals - increased to reduce spam

# Trading pairs to monitor
trading_pairs:
  - symbol: "ADA/USDT"
    enabled: true
    min_profit: 0.02
  - symbol: "LABUBU/USDT"
    enabled: true
    min_profit: 0.02
  - symbol: "SHM/USDT"
    enabled: true
    min_profit: 0.02
  - symbol: "SQD/USDT"
    enabled: true
    min_profit: 0.02
  - symbol: "ESPORTS/USDT"
    enabled: true
    min_profit: 0.02
  - symbol: "LAUNCHCOIN/USDT"
    enabled: true
    min_profit: 0.02
  - symbol: "MORI/USDT"
    enabled: true
    min_profit: 0.02

# Exchange configuration - Conservative settings to avoid rate limiting
exchanges:
  mexc:
    enabled: true
    rate_limit: 300 # Much more conservative - 5 requests per minute
    timeout: 20 # Longer timeout to be less aggressive
    commission_rate: 0.001 # 0.1% trading fee
    position_limit: 1000 # USD limit for positions
    api_urls:
      - "https://api.mexc.global" # Use the working URL first
      - "https://api.mexc.com/api/v3"
  gate:
    enabled: true
    rate_limit: 180 # Very conservative - 3 requests per minute
    timeout: 25 # Even longer timeout
    commission_rate: 0.0007 # 0.07% trading fee
    position_limit: 1500 # USD limit for positions
    api_urls:
      - "https://api.gateio.ws"
      - "https://data.gate.io"
  lbank:
    enabled: true
    rate_limit: 120 # Extremely conservative - 2 requests per minute
    timeout: 30 # Longest timeout for problematic exchange
    commission_rate: 0.001 # 0.1% trading fee
    position_limit: 800 # USD limit for positions
    api_urls:
      - "https://api.lbank.info"
      - "https://api.lbank.me"
  # mock_exchange_1:
  #   enabled: true
  #   type: mock
  # mock_exchange_2:
  #   enabled: true
  #   type: mock

# Database settings
database:
  path: "data/arbitrage.db"
  backup_interval: 3600 # seconds
  cleanup_days: 30

# Logging configuration
logging:
  level: "INFO"
  file_path: "logs/arbitrage.log"
  max_file_size: "10MB"
  backup_count: 5
  console_output: true

# WebSocket configuration - Improved stability settings
websocket:
  enabled: true
  ping_interval: 45 # seconds - Increased for better stability
  ping_timeout: 20 # seconds - Longer timeout to handle network delays
  max_reconnect_attempts: 15 # More attempts for better resilience
  initial_reconnect_delay: 2.0 # seconds - Longer initial delay
  max_reconnect_delay: 120.0 # seconds - Longer max delay
  reconnect_backoff_factor: 1.5 # Gentler backoff progression
  connection_timeout: 30 # seconds - Longer connection timeout
  message_timeout: 10 # seconds - Longer message timeout

# Risk management configuration
risk_management:
  enabled: true
  min_profit_percent: 0.02 # 2% minimum profit
  max_profit_percent: 50.0 # 50% maximum (likely data error)
  max_position_usd: 1000.0 # Maximum position size
  max_daily_volume_usd: 10000.0 # Maximum daily trading volume
  max_exchange_exposure_percent: 40.0 # Max 40% exposure per exchange
  max_slippage_percent: 0.5 # 0.5% maximum slippage
  max_total_fees_percent: 1.0 # 1% maximum total fees
  max_price_change_percent: 10.0 # 10% max price change in 1 hour
  min_volume_usd: 1000.0 # Minimum 24h volume
  max_signal_age_seconds: 30.0 # Maximum signal age
  min_cooldown_seconds: 60.0 # Minimum time between trades on same pair
  max_execution_time_seconds: 10.0 # Maximum execution time
  max_risk_score: 0.7 # Maximum acceptable risk score
  min_confidence: 0.6 # Minimum confidence level
  circuit_breaker_failure_threshold: 5 # Failures before circuit breaker opens
  circuit_breaker_recovery_time: 300 # Seconds before circuit breaker can close

# Profit calculation configuration
profit_calculation:
  default_trade_amount_usd: 100.0 # Default trade amount
  include_withdrawal_fees: true # Include withdrawal fees in calculations
  include_network_fees: true # Include network fees in calculations
  slippage_estimation_enabled: true # Enable slippage estimation
  orderbook_depth_analysis: false # Future feature

# Telegram settings (set via environment variables)
telegram:
  enabled: true
  parse_mode: "HTML"
  disable_web_page_preview: true
  message_template: |
    📈💸 <b>ARBITRAJ FIRSATI YAKALANDI!</b> 💸📈

    🔄 <b>Parite:</b> <code>{symbol}</code>
    💹 <b>Potansiyel Kar:</b> <code>{profit_percent}%</code>

    🟢 <b>AL (UZUN)</b> ➤ <b>{buy_exchange}</b> 
    💲 <b>Alış Fiyatı:</b> <code>${buy_price}</code>
    📈 <b>Mevcut Kar:</b> <code>%{buy_current_profit}</code>
    📉 <b>Sapma:</b> <code>%{buy_deviation}</code>
    🕒 <b>Dönem:</b> <code>{period}</code>

    🔴 <b>SAT (KISA)</b> ➤ <b>{sell_exchange}</b>
    💲 <b>Satış Fiyatı:</b> <code>${sell_price}</code>
    📈 <b>Mevcut Kar:</b> <code>%{sell_current_profit}</code>
    📉 <b>Sapma:</b> <code>%{sell_deviation}</code>
    🕒 <b>Dönem:</b> <code>{period}</code>

    📊 <b>Spread:</b> <code>${price_diff}</code>
    🌐 <b>Kur Farkı Analizi</b>:
      🔹 <b>Gerçek Kar:</b> <code>%{profit_percent}</code>
      🔸 <b>Kur Oranı:</b> <code>%{exchange_rate}</code>
      🧾 <b>Komisyon:</b> <code>%{commission_rate}</code>

    ⏱️ <b>Zaman:</b> <code>{timestamp}</code>
    📬 <i>Bu fırsat anlık hesaplama ile oluşturulmuştur. Lütfen teyit ederek işlem yapınız.</i>
