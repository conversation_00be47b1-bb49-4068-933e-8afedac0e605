#!/usr/bin/env python3
"""
Real-time performance monitoring CLI tool.
"""
import asyncio
import json
import time
import sys
from datetime import datetime
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.monitoring.performance_monitor import performance_monitor
from src.monitoring.optimizer import performance_optimizer
from src.monitoring.dashboard import performance_dashboard


class PerformanceMonitorCLI:
    """Command-line interface for performance monitoring."""
    
    def __init__(self):
        self.is_running = False
        self.update_interval = 2.0  # seconds
    
    async def start_monitoring(self):
        """Start real-time performance monitoring."""
        print("🚀 Starting Performance Monitor CLI")
        print("=" * 60)
        print("Press Ctrl+C to stop monitoring")
        print("=" * 60)
        
        # Initialize monitoring components
        await performance_monitor.start_monitoring(interval_seconds=1.0)
        await performance_optimizer.start_optimization(interval_minutes=5)
        await performance_dashboard.start_dashboard(update_interval=2.0)
        
        self.is_running = True
        
        try:
            while self.is_running:
                await self._display_performance_data()
                await asyncio.sleep(self.update_interval)
        
        except KeyboardInterrupt:
            print("\n⚠️  Monitoring stopped by user")
        
        finally:
            await self._cleanup()
    
    async def _display_performance_data(self):
        """Display current performance data."""
        # Clear screen (works on most terminals)
        print("\033[2J\033[H", end="")
        
        # Header
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"📊 Performance Monitor - {current_time}")
        print("=" * 60)
        
        # Get dashboard data
        dashboard_data = performance_dashboard.get_dashboard_data()
        
        if not dashboard_data:
            print("⏳ Waiting for performance data...")
            return
        
        # System Overview
        await self._display_system_overview(dashboard_data.get('system_overview', {}))
        
        # Latency Metrics
        await self._display_latency_metrics(dashboard_data.get('latency_metrics', {}))
        
        # Component Performance
        await self._display_component_performance(dashboard_data.get('component_performance', {}))
        
        # WebSocket Status
        await self._display_websocket_status(dashboard_data.get('websocket_status', {}))
        
        # Alerts and Warnings
        await self._display_alerts(dashboard_data.get('alerts_and_warnings', {}))
        
        # Optimization Status
        await self._display_optimization_status(dashboard_data.get('optimization_status', {}))
    
    async def _display_system_overview(self, system_data: dict):
        """Display system overview section."""
        print("\n🖥️  SYSTEM OVERVIEW")
        print("-" * 30)
        
        status = system_data.get('status', 'unknown')
        status_icon = "✅" if status == 'healthy' else "⚠️" if status == 'warning' else "❌"
        
        uptime_hours = system_data.get('uptime_seconds', 0) / 3600
        
        print(f"Status: {status_icon} {status.upper()}")
        print(f"Uptime: {uptime_hours:.1f} hours")
        print(f"CPU: {system_data.get('cpu_percent', 0):.1f}%")
        print(f"Memory: {system_data.get('memory_mb', 0):.1f} MB")
        print(f"Messages/sec: {system_data.get('messages_per_second', 0):.1f}")
        print(f"Error Rate: {system_data.get('error_rate', 0):.2f}%")
        print(f"Active Connections: {system_data.get('active_connections', 0)}")
    
    async def _display_latency_metrics(self, latency_data: dict):
        """Display latency metrics section."""
        print("\n⚡ LATENCY METRICS")
        print("-" * 30)
        
        current_latency = latency_data.get('current_latency_ms', 0)
        target_latency = latency_data.get('target_latency_ms', 100)
        
        # Color coding for latency
        if current_latency < target_latency:
            latency_icon = "✅"
        elif current_latency < target_latency * 1.5:
            latency_icon = "⚠️"
        else:
            latency_icon = "❌"
        
        print(f"Current: {latency_icon} {current_latency:.2f}ms")
        print(f"Target: {target_latency}ms")
        print(f"Average: {latency_data.get('average_latency_ms', 0):.2f}ms")
        print(f"P95: {latency_data.get('p95_latency_ms', 0):.2f}ms")
        print(f"Min/Max: {latency_data.get('min_latency_ms', 0):.2f}/{latency_data.get('max_latency_ms', 0):.2f}ms")
        
        trend = latency_data.get('latency_trend', 'stable')
        trend_icon = "📈" if trend == 'increasing' else "📉" if trend == 'decreasing' else "➡️"
        print(f"Trend: {trend_icon} {trend}")
    
    async def _display_component_performance(self, component_data: dict):
        """Display component performance section."""
        print("\n🔧 COMPONENT PERFORMANCE")
        print("-" * 30)
        
        components = component_data.get('components', {})
        
        if not components:
            print("No component data available")
            return
        
        print(f"Total Components: {component_data.get('total_components', 0)}")
        print(f"Healthy: {component_data.get('healthy_components', 0)} | "
              f"Warning: {component_data.get('warning_components', 0)} | "
              f"Critical: {component_data.get('critical_components', 0)}")
        
        # Show top 5 slowest components
        sorted_components = sorted(
            components.items(),
            key=lambda x: x[1].get('average_execution_time_ms', 0),
            reverse=True
        )[:5]
        
        if sorted_components:
            print("\nSlowest Components:")
            for name, metrics in sorted_components:
                status = metrics.get('status', 'unknown')
                status_icon = "✅" if status == 'healthy' else "⚠️" if status == 'warning' else "❌"
                avg_time = metrics.get('average_execution_time_ms', 0)
                error_rate = metrics.get('error_rate', 0)
                print(f"  {status_icon} {name}: {avg_time:.2f}ms (errors: {error_rate:.1f}%)")
    
    async def _display_websocket_status(self, websocket_data: dict):
        """Display WebSocket status section."""
        print("\n🔌 WEBSOCKET STATUS")
        print("-" * 30)
        
        connected_exchanges = websocket_data.get('connected_exchanges', [])
        total_exchanges = websocket_data.get('total_exchanges', 0)
        connection_health = websocket_data.get('connection_health', 0)
        
        health_icon = "✅" if connection_health > 0.8 else "⚠️" if connection_health > 0.5 else "❌"
        
        print(f"Status: {health_icon} {websocket_data.get('status', 'unknown').upper()}")
        print(f"Connected: {len(connected_exchanges)}/{total_exchanges}")
        print(f"Health: {connection_health * 100:.1f}%")
        print(f"Total Messages: {websocket_data.get('total_messages', 0):,}")
        print(f"Subscribed Symbols: {websocket_data.get('subscribed_symbols', 0)}")
        print(f"Avg Latency: {websocket_data.get('average_latency_ms', 0):.2f}ms")
        
        if connected_exchanges:
            print(f"Exchanges: {', '.join(connected_exchanges)}")
    
    async def _display_alerts(self, alerts_data: dict):
        """Display alerts and warnings section."""
        alerts = alerts_data.get('alerts', [])
        warnings = alerts_data.get('warnings', [])
        
        if alerts or warnings:
            print("\n🚨 ALERTS & WARNINGS")
            print("-" * 30)
            
            for alert in alerts[-3:]:  # Show last 3 alerts
                print(f"❌ ALERT: {alert.get('message', 'Unknown alert')}")
            
            for warning in warnings[-3:]:  # Show last 3 warnings
                print(f"⚠️  WARNING: {warning.get('message', 'Unknown warning')}")
        
        overall_status = alerts_data.get('overall_status', 'unknown')
        if overall_status == 'healthy':
            print("\n✅ All systems healthy")
    
    async def _display_optimization_status(self, optimization_data: dict):
        """Display optimization status section."""
        print("\n🎯 OPTIMIZATION STATUS")
        print("-" * 30)
        
        is_optimizing = optimization_data.get('is_optimizing', False)
        optimization_level = optimization_data.get('optimization_level', 'unknown')
        total_optimizations = optimization_data.get('total_optimizations', 0)
        avg_improvement = optimization_data.get('average_improvement', 0)
        
        status_icon = "🔄" if is_optimizing else "⏸️"
        
        print(f"Status: {status_icon} {'ACTIVE' if is_optimizing else 'INACTIVE'}")
        print(f"Level: {optimization_level.upper()}")
        print(f"Total Optimizations: {total_optimizations}")
        print(f"Avg Improvement: {avg_improvement:.1f}%")
        
        recent_recommendations = optimization_data.get('recent_recommendations', [])
        if recent_recommendations:
            print(f"Recent Recommendations: {len(recent_recommendations)}")
            for rec in recent_recommendations[-2:]:  # Show last 2
                print(f"  • {rec.get('recommendation', 'Unknown')}")
    
    async def _cleanup(self):
        """Cleanup monitoring components."""
        try:
            await performance_monitor.stop_monitoring()
            await performance_optimizer.stop_optimization()
            await performance_dashboard.stop_dashboard()
        except Exception as e:
            print(f"Error during cleanup: {e}")


async def main():
    """Main CLI entry point."""
    cli = PerformanceMonitorCLI()
    
    try:
        await cli.start_monitoring()
        return 0
    except Exception as e:
        print(f"❌ Error: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
