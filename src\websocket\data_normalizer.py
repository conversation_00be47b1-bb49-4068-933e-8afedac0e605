"""
Data normalization for unified exchange data format.
"""
from dataclasses import dataclass
from datetime import datetime
from typing import Dict, Any, Optional
from decimal import Decimal

from src.utils.logger import logger


@dataclass
class NormalizedTickerData:
    """Normalized ticker data structure for all exchanges."""
    symbol: str
    bid: float
    ask: float
    last: float
    volume: float
    timestamp: datetime
    exchange: str
    
    # Optional fields
    high: Optional[float] = None
    low: Optional[float] = None
    open: Optional[float] = None
    close: Optional[float] = None
    change: Optional[float] = None
    change_percent: Optional[float] = None
    
    @property
    def mid_price(self) -> float:
        """Calculate mid price from bid/ask."""
        return (self.bid + self.ask) / 2
    
    @property
    def spread(self) -> float:
        """Calculate bid-ask spread."""
        return self.ask - self.bid
    
    @property
    def spread_percent(self) -> float:
        """Calculate bid-ask spread as percentage."""
        if self.mid_price > 0:
            return (self.spread / self.mid_price) * 100
        return 0.0


class DataNormalizer:
    """Normalizes exchange-specific data to unified format."""
    
    @staticmethod
    def normalize_gate_ticker(data: Dict[str, Any], symbol: str) -> Optional[NormalizedTickerData]:
        """Normalize Gate.io WebSocket ticker data."""
        try:
            # Gate.io ticker format: {"method": "ticker.update", "params": [symbol, data]}
            if data.get("method") == "ticker.update" and "params" in data:
                params = data["params"]
                if len(params) >= 2:
                    ticker_data = params[1]

                    # Gate.io provides bid/ask as strings, need to convert
                    bid = float(ticker_data.get("highest_bid", 0))
                    ask = float(ticker_data.get("lowest_ask", 0))
                    last = float(ticker_data.get("last", 0))

                    # Use last price as fallback if bid/ask not available
                    if bid == 0:
                        bid = last * 0.999  # Approximate bid
                    if ask == 0:
                        ask = last * 1.001  # Approximate ask

                    return NormalizedTickerData(
                        symbol=symbol,
                        bid=bid,
                        ask=ask,
                        last=last,
                        volume=float(ticker_data.get("base_volume", 0)),
                        timestamp=datetime.now(),
                        exchange="gate",
                        high=float(ticker_data.get("high_24h", 0)) if ticker_data.get("high_24h") else None,
                        low=float(ticker_data.get("low_24h", 0)) if ticker_data.get("low_24h") else None,
                        change_percent=float(ticker_data.get("change_percentage", 0)) if ticker_data.get("change_percentage") else None
                    )

        except (ValueError, KeyError, TypeError) as e:
            logger.error(f"Error normalizing Gate.io ticker data: {e}")

        return None
    
    @staticmethod
    def normalize_mexc_ticker(data: Dict[str, Any], symbol: str) -> Optional[NormalizedTickerData]:
        """Normalize MEXC WebSocket ticker data."""
        try:
            # MEXC ticker format: {"stream": "symbol@ticker", "data": {...}}
            if "data" in data and "stream" in data:
                ticker_data = data["data"]

                # MEXC uses different field names
                bid = float(ticker_data.get("b", 0))  # Best bid price
                ask = float(ticker_data.get("a", 0))  # Best ask price
                last = float(ticker_data.get("c", 0))  # Last price

                # Use last price as fallback if bid/ask not available
                if bid == 0:
                    bid = last * 0.999
                if ask == 0:
                    ask = last * 1.001

                # Handle timestamp
                timestamp = datetime.now()
                if ticker_data.get("E"):
                    try:
                        timestamp = datetime.fromtimestamp(int(ticker_data.get("E")) / 1000)
                    except (ValueError, OSError):
                        timestamp = datetime.now()

                return NormalizedTickerData(
                    symbol=symbol,
                    bid=bid,
                    ask=ask,
                    last=last,
                    volume=float(ticker_data.get("v", 0)),  # Volume
                    timestamp=timestamp,
                    exchange="mexc",
                    high=float(ticker_data.get("h", 0)) if ticker_data.get("h") else None,
                    low=float(ticker_data.get("l", 0)) if ticker_data.get("l") else None,
                    open=float(ticker_data.get("o", 0)) if ticker_data.get("o") else None,
                    change_percent=float(ticker_data.get("P", 0)) if ticker_data.get("P") else None
                )

        except (ValueError, KeyError, TypeError) as e:
            logger.error(f"Error normalizing MEXC ticker data: {e}")

        return None
    
    @staticmethod
    def normalize_lbank_ticker(data: Dict[str, Any], symbol: str) -> Optional[NormalizedTickerData]:
        """Normalize LBank WebSocket ticker data."""
        try:
            # LBank ticker format: {"type": "ticker", "data": {...}}
            if data.get("type") == "ticker" and "data" in data:
                ticker_data = data["data"]

                # LBank structure can vary, handle different formats
                if "ticker" in ticker_data:
                    # Format: {"data": {"ticker": {...}}}
                    inner_ticker = ticker_data["ticker"]
                    bid = float(inner_ticker.get("buy", 0))
                    ask = float(inner_ticker.get("sell", 0))
                    last = float(inner_ticker.get("latest", 0))
                    volume = float(inner_ticker.get("vol", 0))
                    high = float(inner_ticker.get("high", 0)) if inner_ticker.get("high") else None
                    low = float(inner_ticker.get("low", 0)) if inner_ticker.get("low") else None
                else:
                    # Direct format: {"data": {"buy": ..., "sell": ...}}
                    bid = float(ticker_data.get("buy", 0))
                    ask = float(ticker_data.get("sell", 0))
                    last = float(ticker_data.get("latest", 0))
                    volume = float(ticker_data.get("vol", 0))
                    high = float(ticker_data.get("high", 0)) if ticker_data.get("high") else None
                    low = float(ticker_data.get("low", 0)) if ticker_data.get("low") else None

                # Use last price as fallback
                if bid == 0:
                    bid = last * 0.999
                if ask == 0:
                    ask = last * 1.001

                # Handle timestamp
                timestamp = datetime.now()
                if ticker_data.get("timestamp"):
                    try:
                        timestamp = datetime.fromtimestamp(int(ticker_data.get("timestamp")) / 1000)
                    except (ValueError, OSError):
                        timestamp = datetime.now()

                return NormalizedTickerData(
                    symbol=symbol,
                    bid=bid,
                    ask=ask,
                    last=last,
                    volume=volume,
                    timestamp=timestamp,
                    exchange="lbank",
                    high=high,
                    low=low
                )

        except (ValueError, KeyError, TypeError) as e:
            logger.error(f"Error normalizing LBank ticker data: {e}")

        return None
    
    @staticmethod
    def normalize_ticker(exchange: str, data: Dict[str, Any], symbol: str) -> Optional[NormalizedTickerData]:
        """Normalize ticker data based on exchange."""
        normalizers = {
            "gate": DataNormalizer.normalize_gate_ticker,
            "mexc": DataNormalizer.normalize_mexc_ticker,
            "lbank": DataNormalizer.normalize_lbank_ticker
        }
        
        normalizer = normalizers.get(exchange.lower())
        if normalizer:
            return normalizer(data, symbol)
        
        logger.warning(f"No normalizer found for exchange: {exchange}")
        return None
    
    @staticmethod
    def validate_ticker_data(ticker: NormalizedTickerData) -> bool:
        """Validate normalized ticker data."""
        try:
            # Basic validation
            if not ticker.symbol or not ticker.exchange:
                return False
            
            # Price validation
            if ticker.bid <= 0 or ticker.ask <= 0 or ticker.last <= 0:
                return False
            
            # Spread validation (ask should be >= bid)
            if ticker.ask < ticker.bid:
                return False
            
            # Volume validation
            if ticker.volume < 0:
                return False
            
            # Timestamp validation (not too old)
            age_seconds = (datetime.now() - ticker.timestamp).total_seconds()
            if age_seconds > 300:  # 5 minutes
                logger.warning(f"Ticker data too old: {age_seconds}s for {ticker.symbol}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating ticker data: {e}")
            return False
    
    @staticmethod
    def to_price_data(ticker: NormalizedTickerData):
        """Convert normalized ticker to PriceData model."""
        from src.database.models import PriceData
        
        return PriceData(
            exchange=ticker.exchange,
            symbol=ticker.symbol,
            price=ticker.last,
            volume=ticker.volume,
            bid=ticker.bid,
            ask=ticker.ask,
            timestamp=ticker.timestamp
        )
