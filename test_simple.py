#!/usr/bin/env python3
"""
Simple test to check if basic imports work.
"""
import sys
from pathlib import Path

def test_basic_imports():
    """Test basic imports."""
    print("Testing basic imports...")
    
    try:
        from src.config.settings import config
        print("✅ Config imported successfully")
        
        from src.utils.logger import logger
        print("✅ Logger imported successfully")
        
        from src.database.models import db_manager
        print("✅ Database manager imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_websocket_imports():
    """Test WebSocket imports."""
    print("\nTesting WebSocket imports...")
    
    try:
        from src.websocket.data_normalizer import NormalizedTickerData
        print("✅ Data normalizer imported successfully")
        
        from src.websocket.manager import WebSocketManager
        print("✅ WebSocket manager imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ WebSocket import failed: {e}")
        return False

def test_arbitrage_imports():
    """Test arbitrage imports."""
    print("\nTesting arbitrage imports...")
    
    try:
        from src.arbitrage.engine import ArbitrageEngine
        print("✅ Arbitrage engine imported successfully")
        
        from src.profit.calculator import ProfitCalculator
        print("✅ Profit calculator imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Arbitrage import failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Simple Import Test")
    print("=" * 30)
    
    tests = [
        test_basic_imports,
        test_websocket_imports,
        test_arbitrage_imports
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test crashed: {e}")
            results.append(False)
    
    print("\n" + "=" * 30)
    if all(results):
        print("🎉 ALL IMPORTS WORKING!")
        print("You can now run: python main.py")
    else:
        print("❌ Some imports failed")
        print("Check the errors above")
    
    return 0 if all(results) else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
