#!/usr/bin/env python3
"""
Unit tests for real-time arbitrage engine.
"""
import pytest
import asyncio
import sys
from datetime import datetime
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.arbitrage.realtime_engine import RealtimeArbitrageEngine, OpportunityMetrics
from src.arbitrage.handlers import TelegramNotificationHandler, DatabaseLogHandler
from src.websocket.data_normalizer import NormalizedTickerData
from src.profit.calculator import ProfitCalculation
from src.risk.manager import RiskAssessment, RiskDecision, RiskLevel
from src.database.models import ArbitrageSignal


class TestOpportunityMetrics:
    """Test opportunity metrics functionality."""
    
    def test_default_metrics(self):
        """Test default opportunity metrics."""
        metrics = OpportunityMetrics()
        
        assert metrics.total_opportunities == 0
        assert metrics.approved_opportunities == 0
        assert metrics.rejected_opportunities == 0
        assert metrics.high_risk_opportunities == 0
        assert metrics.average_profit_percent == 0.0
        assert metrics.average_risk_score == 0.0
        assert metrics.last_opportunity_time is None


class MockArbitrageHandler:
    """Mock arbitrage handler for testing."""
    
    def __init__(self):
        self.handled_opportunities = []
        self.handle_calls = 0
    
    async def handle_opportunity(self, signal, risk_assessment):
        self.handle_calls += 1
        self.handled_opportunities.append((signal, risk_assessment))
        return True


class TestRealtimeArbitrageEngine:
    """Test real-time arbitrage engine functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.engine = RealtimeArbitrageEngine()
        
        # Create test ticker data
        self.ticker_data = [
            NormalizedTickerData(
                symbol="ADA/USDT",
                bid=0.4995,
                ask=0.5000,
                last=0.4998,
                volume=1000000,
                timestamp=datetime.now(),
                exchange="gate"
            ),
            NormalizedTickerData(
                symbol="ADA/USDT",
                bid=0.5020,
                ask=0.5025,
                last=0.5022,
                volume=800000,
                timestamp=datetime.now(),
                exchange="mexc"
            )
        ]
    
    def test_engine_initialization(self):
        """Test engine initialization."""
        engine = RealtimeArbitrageEngine()
        
        assert engine.is_running is False
        assert len(engine.handlers) == 0
        assert isinstance(engine.metrics, OpportunityMetrics)
        assert len(engine.processing_times) == 0
        assert len(engine.recent_opportunities) == 0
        assert len(engine.symbol_metrics) == 0
    
    def test_add_handler(self):
        """Test adding arbitrage handlers."""
        engine = RealtimeArbitrageEngine()
        handler = MockArbitrageHandler()
        
        engine.add_handler(handler)
        
        assert len(engine.handlers) == 1
        assert engine.handlers[0] == handler
    
    def test_should_analyze_symbol_first_time(self):
        """Test symbol analysis rate limiting - first time."""
        engine = RealtimeArbitrageEngine()
        
        result = engine._should_analyze_symbol("ADA/USDT")
        
        assert result is True
        assert "ADA/USDT" in engine.recent_opportunities
    
    def test_should_analyze_symbol_rate_limited(self):
        """Test symbol analysis rate limiting - too frequent."""
        engine = RealtimeArbitrageEngine()
        
        # First call should succeed
        result1 = engine._should_analyze_symbol("ADA/USDT")
        assert result1 is True
        
        # Immediate second call should be rate limited
        result2 = engine._should_analyze_symbol("ADA/USDT")
        assert result2 is False
    
    @pytest.mark.asyncio
    async def test_find_opportunities_profitable(self):
        """Test finding profitable arbitrage opportunities."""
        engine = RealtimeArbitrageEngine()
        
        with patch('src.arbitrage.realtime_engine.profit_calculator') as mock_calc:
            # Mock profitable calculation
            mock_profit_calc = MagicMock()
            mock_profit_calc.is_profitable = True
            mock_profit_calc.symbol = "ADA/USDT"
            mock_profit_calc.buy_exchange = "gate"
            mock_profit_calc.sell_exchange = "mexc"
            mock_calc.calculate_profit.return_value = mock_profit_calc
            
            opportunities = await engine._find_opportunities(self.ticker_data)
            
            assert len(opportunities) > 0
            assert mock_calc.calculate_profit.called
    
    @pytest.mark.asyncio
    async def test_find_opportunities_no_profit(self):
        """Test finding opportunities when none are profitable."""
        engine = RealtimeArbitrageEngine()
        
        with patch('src.arbitrage.realtime_engine.profit_calculator') as mock_calc:
            # Mock unprofitable calculation
            mock_profit_calc = MagicMock()
            mock_profit_calc.is_profitable = False
            mock_calc.calculate_profit.return_value = mock_profit_calc
            
            opportunities = await engine._find_opportunities(self.ticker_data)
            
            assert len(opportunities) == 0
    
    @pytest.mark.asyncio
    async def test_process_opportunity_approved(self):
        """Test processing an approved opportunity."""
        engine = RealtimeArbitrageEngine()
        handler = MockArbitrageHandler()
        engine.add_handler(handler)
        
        # Create mock profit calculation
        profit_calc = MagicMock()
        profit_calc.symbol = "ADA/USDT"
        profit_calc.buy_exchange = "gate"
        profit_calc.sell_exchange = "mexc"
        profit_calc.buy_price = 0.5000
        profit_calc.sell_price = 0.5020
        profit_calc.profit_percent = 2.0
        profit_calc.net_profit = 2.0
        profit_calc.calculation_time = datetime.now()
        profit_calc.trade_amount = 200.0
        profit_calc.total_fees = 1.0
        profit_calc.buy_slippage = 0.05
        profit_calc.sell_slippage = 0.05
        profit_calc.roi_percent = 2.0
        
        with patch('src.arbitrage.realtime_engine.risk_manager') as mock_risk:
            with patch('src.arbitrage.realtime_engine.db_manager') as mock_db:
                # Mock approved risk assessment
                mock_assessment = RiskAssessment(
                    decision=RiskDecision.APPROVE,
                    risk_level=RiskLevel.LOW,
                    risk_score=0.2,
                    confidence=0.8,
                    reasons=["Low risk opportunity"]
                )
                mock_risk.assess_risk.return_value = mock_assessment
                mock_db.insert_arbitrage_signal.return_value = 123
                
                await engine._process_opportunity(profit_calc, self.ticker_data)
                
                # Check that handler was called
                assert handler.handle_calls == 1
                assert len(handler.handled_opportunities) == 1
                
                # Check metrics were updated
                assert engine.metrics.total_opportunities == 1
                assert engine.metrics.approved_opportunities == 1
                assert engine.metrics.rejected_opportunities == 0
    
    @pytest.mark.asyncio
    async def test_process_opportunity_rejected(self):
        """Test processing a rejected opportunity."""
        engine = RealtimeArbitrageEngine()
        handler = MockArbitrageHandler()
        engine.add_handler(handler)
        
        # Create mock profit calculation
        profit_calc = MagicMock()
        profit_calc.symbol = "ADA/USDT"
        profit_calc.buy_exchange = "gate"
        profit_calc.sell_exchange = "mexc"
        
        with patch('src.arbitrage.realtime_engine.risk_manager') as mock_risk:
            # Mock rejected risk assessment
            mock_assessment = RiskAssessment(
                decision=RiskDecision.REJECT,
                risk_level=RiskLevel.HIGH,
                risk_score=0.8,
                confidence=0.3,
                reasons=["High risk", "Low confidence"]
            )
            mock_risk.assess_risk.return_value = mock_assessment
            
            await engine._process_opportunity(profit_calc, self.ticker_data)
            
            # Check that handler was NOT called
            assert handler.handle_calls == 0
            
            # Check metrics were updated
            assert engine.metrics.total_opportunities == 1
            assert engine.metrics.approved_opportunities == 0
            assert engine.metrics.rejected_opportunities == 1
    
    def test_create_signal_from_profit_calc(self):
        """Test creating arbitrage signal from profit calculation."""
        engine = RealtimeArbitrageEngine()
        
        # Create mock profit calculation
        profit_calc = MagicMock()
        profit_calc.symbol = "ADA/USDT"
        profit_calc.buy_exchange = "gate"
        profit_calc.sell_exchange = "mexc"
        profit_calc.buy_price = 0.5000
        profit_calc.sell_price = 0.5020
        profit_calc.profit_percent = 2.0
        profit_calc.net_profit = 2.0
        profit_calc.calculation_time = datetime.now()
        profit_calc.trade_amount = 200.0
        profit_calc.total_fees = 1.0
        profit_calc.buy_slippage = 0.05
        profit_calc.sell_slippage = 0.05
        profit_calc.roi_percent = 2.0
        
        # Create mock risk assessment
        risk_assessment = RiskAssessment(
            decision=RiskDecision.APPROVE,
            risk_level=RiskLevel.LOW,
            risk_score=0.2,
            confidence=0.8,
            reasons=["Low risk opportunity"]
        )
        
        with patch('src.arbitrage.realtime_engine.config') as mock_config:
            mock_config.bot.monitoring_interval = 15
            
            signal = engine._create_signal_from_profit_calc(profit_calc, risk_assessment)
            
            assert isinstance(signal, ArbitrageSignal)
            assert signal.symbol == "ADA/USDT"
            assert signal.buy_exchange == "gate"
            assert signal.sell_exchange == "mexc"
            assert signal.buy_price == 0.5000
            assert signal.sell_price == 0.5020
            assert signal.profit_percent == 2.0
            assert signal.action == "LONG"
            assert signal.sent_to_telegram is False
    
    def test_update_metrics(self):
        """Test metrics updating."""
        engine = RealtimeArbitrageEngine()
        
        # Create mock opportunities
        opportunities = [
            MagicMock(profit_percent=2.0, risk_score=0.2),
            MagicMock(profit_percent=3.0, risk_score=0.3)
        ]
        
        engine._update_metrics("ADA/USDT", opportunities)
        
        # Check symbol metrics
        assert "ADA/USDT" in engine.symbol_metrics
        symbol_metrics = engine.symbol_metrics["ADA/USDT"]
        assert symbol_metrics.total_opportunities == 2
        assert symbol_metrics.average_profit_percent == 2.5
        assert symbol_metrics.average_risk_score == 0.25
        assert symbol_metrics.last_opportunity_time is not None
    
    def test_get_performance_stats(self):
        """Test performance statistics reporting."""
        engine = RealtimeArbitrageEngine()
        engine.processing_times = [10.0, 15.0, 20.0]  # Mock processing times
        engine.metrics.total_opportunities = 5
        engine.metrics.approved_opportunities = 3
        engine.metrics.rejected_opportunities = 2
        
        with patch('src.arbitrage.realtime_engine.exchange_websocket_manager') as mock_ws:
            with patch('src.arbitrage.realtime_engine.risk_manager') as mock_risk:
                mock_ws.get_performance_stats.return_value = {"test": "data"}
                mock_risk.get_risk_statistics.return_value = {"risk": "data"}
                
                stats = engine.get_performance_stats()
                
                assert isinstance(stats, dict)
                assert stats['is_running'] is False
                assert stats['metrics']['total_opportunities'] == 5
                assert stats['metrics']['approved_opportunities'] == 3
                assert stats['metrics']['rejected_opportunities'] == 2
                assert stats['performance']['average_processing_time_ms'] == 15.0
                assert stats['performance']['handlers_count'] == 0
                assert 'websocket_stats' in stats
                assert 'risk_stats' in stats


class TestArbitrageHandlers:
    """Test arbitrage opportunity handlers."""
    
    def test_telegram_notification_handler_initialization(self):
        """Test Telegram notification handler initialization."""
        handler = TelegramNotificationHandler()
        
        assert len(handler.last_notifications) == 0
        assert handler.notification_count == 0
    
    def test_database_log_handler_initialization(self):
        """Test database log handler initialization."""
        handler = DatabaseLogHandler()
        
        assert handler.logged_count == 0
    
    def test_telegram_handler_get_stats(self):
        """Test Telegram handler statistics."""
        handler = TelegramNotificationHandler()
        handler.notification_count = 5
        handler.last_notifications["ADA/USDT"] = datetime.now()
        
        stats = handler.get_stats()
        
        assert isinstance(stats, dict)
        assert stats['notification_count'] == 5
        assert stats['symbols_notified'] == 1
        assert 'last_notifications' in stats
    
    def test_database_handler_get_stats(self):
        """Test database handler statistics."""
        handler = DatabaseLogHandler()
        handler.logged_count = 10
        
        stats = handler.get_stats()
        
        assert isinstance(stats, dict)
        assert stats['logged_count'] == 10


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
