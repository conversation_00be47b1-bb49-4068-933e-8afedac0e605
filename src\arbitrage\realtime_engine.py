"""
Real-time arbitrage engine with WebSocket integration, advanced profit calculation, and risk management.
"""
import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Callable, Protocol
from dataclasses import dataclass

from src.config.settings import config
from src.database.models import ArbitrageSignal, db_manager
from src.websocket.data_normalizer import NormalizedTickerData
from src.websocket.exchange_websocket_manager import exchange_websocket_manager
from src.profit.calculator import profit_calculator, ProfitCalculation
from src.risk.manager import risk_manager, RiskAssessment, RiskDecision
from src.utils.logger import logger


class ArbitrageHandler(Protocol):
    """Protocol for arbitrage opportunity handlers."""
    
    async def handle_opportunity(self, signal: ArbitrageSignal, 
                               risk_assessment: RiskAssessment) -> bool:
        """Handle an arbitrage opportunity."""
        ...


@dataclass
class OpportunityMetrics:
    """Metrics for arbitrage opportunity analysis."""
    total_opportunities: int = 0
    approved_opportunities: int = 0
    rejected_opportunities: int = 0
    high_risk_opportunities: int = 0
    average_profit_percent: float = 0.0
    average_risk_score: float = 0.0
    last_opportunity_time: Optional[datetime] = None


class RealtimeArbitrageEngine:
    """
    Real-time arbitrage engine that processes WebSocket data streams,
    calculates comprehensive profits, and applies risk management.
    """
    
    def __init__(self):
        self.is_running = False
        self.handlers: List[ArbitrageHandler] = []
        self.metrics = OpportunityMetrics()
        
        # Performance tracking
        self.processing_times: List[float] = []
        self.last_cleanup_time = datetime.now()
        
        # Opportunity tracking
        self.recent_opportunities: Dict[str, datetime] = {}
        self.symbol_metrics: Dict[str, OpportunityMetrics] = {}
        
    async def start(self) -> bool:
        """Start the real-time arbitrage engine."""
        if self.is_running:
            logger.warning("Real-time arbitrage engine already running")
            return True
        
        try:
            # Initialize WebSocket manager
            success = await exchange_websocket_manager.initialize()
            if not success:
                logger.error("Failed to initialize WebSocket manager")
                return False
            
            # Subscribe to trading pairs
            success = await exchange_websocket_manager.subscribe_to_trading_pairs()
            if not success:
                logger.error("Failed to subscribe to trading pairs")
                return False
            
            # Set up data handler
            exchange_websocket_manager.add_arbitrage_callback(self._handle_ticker_data)
            
            self.is_running = True
            logger.info("Real-time arbitrage engine started successfully")
            
            # Start cleanup task
            asyncio.create_task(self._cleanup_loop())
            
            return True
            
        except Exception as e:
            logger.error(f"Error starting real-time arbitrage engine: {e}")
            return False
    
    async def stop(self):
        """Stop the real-time arbitrage engine."""
        if not self.is_running:
            return
        
        self.is_running = False
        
        try:
            await exchange_websocket_manager.stop()
            logger.info("Real-time arbitrage engine stopped")
        except Exception as e:
            logger.error(f"Error stopping real-time arbitrage engine: {e}")
    
    def add_handler(self, handler: ArbitrageHandler):
        """Add an arbitrage opportunity handler."""
        self.handlers.append(handler)
        logger.info(f"Added arbitrage handler: {handler.__class__.__name__}")
    
    async def _handle_ticker_data(self, ticker_data_list: List[NormalizedTickerData]):
        """Handle incoming ticker data and analyze for arbitrage opportunities."""
        if not self.is_running or len(ticker_data_list) < 2:
            return
        
        start_time = datetime.now()
        
        try:
            symbol = ticker_data_list[0].symbol
            
            # Check if we should analyze this symbol (rate limiting)
            if not self._should_analyze_symbol(symbol):
                return
            
            # Find arbitrage opportunities
            opportunities = await self._find_opportunities(ticker_data_list)
            
            # Process each opportunity
            for opportunity in opportunities:
                await self._process_opportunity(opportunity, ticker_data_list)
            
            # Update metrics
            self._update_metrics(symbol, opportunities)
            
            # Track processing time
            processing_time = (datetime.now() - start_time).total_seconds() * 1000  # ms
            self.processing_times.append(processing_time)
            
            # Keep only recent processing times
            if len(self.processing_times) > 1000:
                self.processing_times = self.processing_times[-500:]
            
        except Exception as e:
            logger.error(f"Error handling ticker data: {e}")
    
    def _should_analyze_symbol(self, symbol: str) -> bool:
        """Check if we should analyze this symbol based on rate limiting."""
        now = datetime.now()
        last_analysis = self.recent_opportunities.get(symbol)
        
        if last_analysis:
            time_since_last = (now - last_analysis).total_seconds()
            min_interval = config.bot.max_signal_frequency / 4  # Allow more frequent analysis
            
            if time_since_last < min_interval:
                return False
        
        self.recent_opportunities[symbol] = now
        return True
    
    async def _find_opportunities(self, ticker_data_list: List[NormalizedTickerData]) -> List[ProfitCalculation]:
        """Find arbitrage opportunities from ticker data."""
        opportunities = []

        # Compare all exchange pairs
        for i in range(len(ticker_data_list)):
            for j in range(i + 1, len(ticker_data_list)):
                ticker1 = ticker_data_list[i]
                ticker2 = ticker_data_list[j]

                try:
                    # Check both directions
                    if ticker1.ask < ticker2.bid:
                        # Buy on ticker1, sell on ticker2
                        profit_calc = profit_calculator.calculate_profit(
                            buy_ticker=ticker1,
                            sell_ticker=ticker2,
                            trade_amount_usd=100.0
                        )

                        if profit_calc.is_profitable:
                            opportunities.append(profit_calc)

                    elif ticker2.ask < ticker1.bid:
                        # Buy on ticker2, sell on ticker1
                        profit_calc = profit_calculator.calculate_profit(
                            buy_ticker=ticker2,
                            sell_ticker=ticker1,
                            trade_amount_usd=100.0
                        )

                        if profit_calc.is_profitable:
                            opportunities.append(profit_calc)

                except Exception as e:
                    logger.error(f"Error calculating profit for {ticker1.exchange}-{ticker2.exchange}: {e}")
                    continue

        return opportunities
    
    async def _process_opportunity(self, profit_calc: ProfitCalculation,
                                 ticker_data_list: List[NormalizedTickerData]):
        """Process a single arbitrage opportunity."""
        try:
            # Perform risk assessment
            risk_assessment = await risk_manager.assess_risk(
                profit_calc=profit_calc,
                ticker_data=ticker_data_list,
                trade_amount_usd=100.0
            )
            
            # Update metrics
            self.metrics.total_opportunities += 1
            
            # Check risk decision
            if risk_assessment.decision == RiskDecision.REJECT:
                self.metrics.rejected_opportunities += 1
                logger.debug(
                    f"Opportunity rejected: {profit_calc.symbol} "
                    f"{profit_calc.buy_exchange}->{profit_calc.sell_exchange} "
                    f"Reasons: {', '.join(risk_assessment.reasons)}"
                )
                return
            
            if risk_assessment.risk_level.value in ['high', 'critical']:
                self.metrics.high_risk_opportunities += 1
            
            # Create arbitrage signal
            signal = self._create_signal_from_profit_calc(profit_calc, risk_assessment)
            
            # Store in database
            signal_id = await db_manager.insert_arbitrage_signal(signal)
            signal.id = signal_id
            
            # Update metrics
            self.metrics.approved_opportunities += 1
            self.metrics.last_opportunity_time = datetime.now()
            
            # Notify handlers
            for handler in self.handlers:
                try:
                    await handler.handle_opportunity(signal, risk_assessment)
                except Exception as e:
                    logger.error(f"Error in arbitrage handler {handler.__class__.__name__}: {e}")
            
            logger.info(
                f"Arbitrage opportunity: {signal.symbol} "
                f"Buy {signal.buy_exchange}@{signal.buy_price:.6f} "
                f"Sell {signal.sell_exchange}@{signal.sell_price:.6f} "
                f"Profit: {signal.profit_percent:.2f}% "
                f"Risk: {risk_assessment.risk_level.value} ({risk_assessment.risk_score:.2f})"
            )
            
        except Exception as e:
            logger.error(f"Error processing opportunity: {e}")
    
    def _create_signal_from_profit_calc(self, profit_calc: ProfitCalculation,
                                       risk_assessment: RiskAssessment) -> ArbitrageSignal:
        """Create ArbitrageSignal from ProfitCalculation and RiskAssessment."""
        return ArbitrageSignal(
            symbol=profit_calc.symbol,
            buy_exchange=profit_calc.buy_exchange,
            sell_exchange=profit_calc.sell_exchange,
            buy_price=profit_calc.buy_price,
            sell_price=profit_calc.sell_price,
            profit_percent=round(profit_calc.profit_percent, 2),
            profit_amount=round(profit_calc.net_profit, 6),
            action="LONG",
            timestamp=profit_calc.calculation_time,
            sent_to_telegram=False,
            # Enhanced fields
            buy_current_profit=round(profit_calc.roi_percent * 0.5, 2),
            sell_current_profit=round(profit_calc.roi_percent * 0.5, 2),
            buy_deviation=round(profit_calc.buy_slippage * 100, 2),
            sell_deviation=round(profit_calc.sell_slippage * 100, 2),
            sell_limit=profit_calc.trade_amount * profit_calc.sell_price,
            period=f"{config.bot.monitoring_interval}s",
            exchange_rate=round(profit_calc.profit_percent + (profit_calc.total_fees / (profit_calc.trade_amount * profit_calc.buy_price) * 100), 2),
            commission_rate=round((profit_calc.total_fees / (profit_calc.trade_amount * profit_calc.buy_price)) * 100, 2)
        )
    
    def _update_metrics(self, symbol: str, opportunities: List[ProfitCalculation]):
        """Update metrics for symbol and overall."""
        if symbol not in self.symbol_metrics:
            self.symbol_metrics[symbol] = OpportunityMetrics()
        
        symbol_metrics = self.symbol_metrics[symbol]
        symbol_metrics.total_opportunities += len(opportunities)
        
        if opportunities:
            # Calculate averages
            total_profit = sum(opp.profit_percent for opp in opportunities)
            total_risk = sum(opp.risk_score for opp in opportunities)
            
            symbol_metrics.average_profit_percent = total_profit / len(opportunities)
            symbol_metrics.average_risk_score = total_risk / len(opportunities)
            symbol_metrics.last_opportunity_time = datetime.now()
            
            # Update global metrics
            if self.metrics.total_opportunities > 0:
                self.metrics.average_profit_percent = (
                    (self.metrics.average_profit_percent * (self.metrics.total_opportunities - len(opportunities)) +
                     total_profit) / self.metrics.total_opportunities
                )
                self.metrics.average_risk_score = (
                    (self.metrics.average_risk_score * (self.metrics.total_opportunities - len(opportunities)) +
                     total_risk) / self.metrics.total_opportunities
                )
    
    async def _cleanup_loop(self):
        """Periodic cleanup of old data."""
        while self.is_running:
            try:
                await asyncio.sleep(300)  # Run every 5 minutes
                await self._cleanup_old_data()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Cleanup loop error: {e}")
    
    async def _cleanup_old_data(self):
        """Clean up old tracking data."""
        now = datetime.now()
        cutoff_time = now - timedelta(hours=1)
        
        # Clean up recent opportunities
        symbols_to_remove = []
        for symbol, timestamp in self.recent_opportunities.items():
            if timestamp < cutoff_time:
                symbols_to_remove.append(symbol)
        
        for symbol in symbols_to_remove:
            del self.recent_opportunities[symbol]
        
        self.last_cleanup_time = now
        
        if symbols_to_remove:
            logger.debug(f"Cleaned up {len(symbols_to_remove)} old opportunity records")
    
    def get_performance_stats(self) -> Dict[str, any]:
        """Get comprehensive performance statistics."""
        avg_processing_time = (
            sum(self.processing_times) / len(self.processing_times)
            if self.processing_times else 0
        )
        
        return {
            'is_running': self.is_running,
            'metrics': {
                'total_opportunities': self.metrics.total_opportunities,
                'approved_opportunities': self.metrics.approved_opportunities,
                'rejected_opportunities': self.metrics.rejected_opportunities,
                'high_risk_opportunities': self.metrics.high_risk_opportunities,
                'average_profit_percent': self.metrics.average_profit_percent,
                'average_risk_score': self.metrics.average_risk_score,
                'last_opportunity_time': self.metrics.last_opportunity_time.isoformat() if self.metrics.last_opportunity_time else None
            },
            'performance': {
                'average_processing_time_ms': avg_processing_time,
                'recent_processing_samples': len(self.processing_times),
                'handlers_count': len(self.handlers)
            },
            'symbol_metrics': {
                symbol: {
                    'total_opportunities': metrics.total_opportunities,
                    'average_profit_percent': metrics.average_profit_percent,
                    'average_risk_score': metrics.average_risk_score,
                    'last_opportunity_time': metrics.last_opportunity_time.isoformat() if metrics.last_opportunity_time else None
                }
                for symbol, metrics in self.symbol_metrics.items()
            },
            'websocket_stats': exchange_websocket_manager.get_performance_stats(),
            'risk_stats': risk_manager.get_risk_statistics()
        }


# Global instance
realtime_arbitrage_engine = RealtimeArbitrageEngine()
