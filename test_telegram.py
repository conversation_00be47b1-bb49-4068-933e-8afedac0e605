#!/usr/bin/env python3
"""
Test script to verify Telegram bot configuration and connectivity.
"""
import asyncio
import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.notifications.telegram_bot import TelegramNotifier
from src.config.settings import config
from src.utils.logger import logger


async def test_telegram_config():
    """Test Telegram configuration and connectivity."""
    print("🔍 Testing Telegram Configuration...")
    
    # Check environment variables
    bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
    chat_id = os.getenv("TELEGRAM_CHAT_ID")
    
    print(f"  📱 Bot Token: {'✅ Set' if bot_token else '❌ Not set'}")
    print(f"  💬 Chat ID: {'✅ Set' if chat_id else '❌ Not set'}")
    
    if not bot_token or not chat_id:
        print("\n❌ Telegram credentials not configured!")
        print("Please set the following environment variables:")
        print("  - TELEGRAM_BOT_TOKEN=your_bot_token")
        print("  - TELEGRAM_CHAT_ID=your_chat_id")
        return False
    
    # Test configuration loading
    try:
        is_configured = config.is_telegram_configured()
        print(f"  ⚙️ Config loaded: {'✅ Yes' if is_configured else '❌ No'}")
        
        if not is_configured:
            print("❌ Telegram configuration validation failed!")
            return False
            
    except Exception as e:
        print(f"❌ Error loading configuration: {e}")
        return False
    
    # Test Telegram bot initialization
    try:
        print("\n🤖 Testing Telegram Bot Connection...")
        telegram_notifier = TelegramNotifier()
        await telegram_notifier.initialize()
        
        if telegram_notifier.is_configured:
            print("✅ Telegram bot initialized successfully!")
            
            # Send test message
            print("📤 Sending test message...")
            success = await telegram_notifier.send_status_update("🧪 Test message from arbitrage bot!")
            
            if success:
                print("✅ Test message sent successfully!")
                return True
            else:
                print(f"❌ Failed to send test message: {telegram_notifier.last_error}")
                return False
        else:
            print(f"❌ Telegram bot initialization failed: {telegram_notifier.last_error}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing Telegram bot: {e}")
        return False


async def main():
    """Main test function."""
    print("🚀 Telegram Bot Test Script")
    print("=" * 50)
    
    try:
        success = await test_telegram_config()
        
        if success:
            print("\n🎉 All Telegram tests passed!")
            print("Your Telegram bot is ready to send notifications.")
            return 0
        else:
            print("\n💥 Telegram tests failed!")
            print("Please check your configuration and try again.")
            return 1
            
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
        return 1
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
